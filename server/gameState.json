{"players": [{"id": "ztxSRPPhOUIrirStAAAB", "name": "CosmicSeeker", "position": 52, "hand": [{"id": "T35", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T32", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "energyCubes": [], "omTemp": [1], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [], "outerScore": 0, "innerScore": 0, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "merchant1", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}}, {"id": "nbHe6J6r3FLupZuIAAAH", "name": "me", "position": 63, "hand": [{"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T43", "type": "travel", "value": 2, "vehicle": "rickshaw"}], "energyCubes": [], "omTemp": [], "omSlotsOuter": [0, 0, 0, 0], "omSlotsInner": [0, 0, 0, 0], "collectedJourneys": [], "outerScore": 0, "innerScore": 0, "didMoveThisTurn": false, "didSelectionActionThisTurn": false, "didTradeThisTurn": false, "character": {"id": "engineer2", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}}], "started": true, "turnIndex": 1, "roundCount": 0, "travelDeck": [{"id": "T34", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T6", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T27", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T37", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T2", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T25", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T24", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T46", "type": "travel", "value": 3, "vehicle": "boat"}, {"id": "T29", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T8", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T39", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T23", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T28", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T45", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T13", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T38", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T48", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T16", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T7", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T9", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T11", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T17", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T31", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T47", "type": "travel", "value": 3, "vehicle": "train"}, {"id": "T36", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T15", "type": "travel", "value": 1, "vehicle": "trek"}, {"id": "T4", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T40", "type": "travel", "value": 1, "vehicle": "cycle"}, {"id": "T12", "type": "travel", "value": 3, "vehicle": "truck"}, {"id": "T18", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T21", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T44", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T5", "type": "travel", "value": 2, "vehicle": "bus"}, {"id": "T1", "type": "travel", "value": 1, "vehicle": "camel"}, {"id": "T26", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T19", "type": "travel", "value": 2, "vehicle": "rickshaw"}, {"id": "T33", "type": "travel", "value": 3, "vehicle": "helicopter"}, {"id": "T42", "type": "travel", "value": 2, "vehicle": "car"}], "eventDeck": [{"id": "E7", "type": "extraHop"}, {"id": "E1", "type": "extraHop"}, {"id": "E6", "type": "extraHop"}, {"id": "E5", "type": "extraHop"}, {"id": "E9", "type": "extraHop"}], "journeyDeckInner": [{"id": "JI13", "locationId": 23, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI22", "locationId": 48, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI23", "locationId": 17, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI17", "locationId": 36, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI24", "locationId": 35, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI2", "locationId": 3, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI15", "locationId": 26, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI1", "locationId": 1, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI19", "locationId": 40, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI16", "locationId": 32, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI8", "locationId": 15, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI6", "locationId": 10, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI20", "locationId": 41, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI18", "locationId": 38, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI14", "locationId": 24, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI5", "locationId": 8, "required": {"bhakti": 1, "gnana": 2}, "reward": {"inner": 27}}, {"id": "JI3", "locationId": 4, "required": {"gnana": 1}, "reward": {"inner": 20}}, {"id": "JI12", "locationId": 21, "required": {"bhakti": 1}, "reward": {"inner": 20}}, {"id": "JI9", "locationId": 16, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI10", "locationId": 19, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}], "journeyDeckOuter": [{"id": "JO20", "locationId": 37, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO25", "locationId": 46, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO24", "locationId": 45, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO18", "locationId": 34, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO5", "locationId": 11, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO15", "locationId": 35, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO14", "locationId": 29, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO3", "locationId": 6, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO6", "locationId": 12, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO26", "locationId": 47, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO21", "locationId": 39, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO1", "locationId": 2, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO16", "locationId": 31, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}, {"id": "JO10", "locationId": 22, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO17", "locationId": 33, "required": {"karma": 1}, "reward": {"outer": 20}}, {"id": "JO11", "locationId": 25, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO4", "locationId": 9, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO23", "locationId": 44, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO13", "locationId": 28, "required": {"karma": 2, "artha": 2}, "reward": {"outer": 35}}, {"id": "JO9", "locationId": 18, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}], "travelDiscard": [{"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}], "eventDiscard": [], "journeyInnerDiscard": [], "journeyOuterDiscard": [], "faceUpTravel": [{"id": "T20", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T14", "type": "travel", "value": 1, "vehicle": "horse"}, {"id": "T30", "type": "travel", "value": 2, "vehicle": "car"}, {"id": "T41", "type": "travel", "value": 2, "vehicle": "bus"}], "faceUpEvent": [{"id": "E3", "type": "extraHop"}, {"id": "E8", "type": "extraHop"}, {"id": "E4", "type": "extraHop"}, {"id": "E2", "type": "extraHop"}], "faceUpJourneyInner": [{"id": "JI4", "locationId": 7, "required": {"bhakti": 2, "gnana": 1}, "reward": {"inner": 27}}, {"id": "JI11", "locationId": 20, "required": {"bhakti": 2, "gnana": 2}, "reward": {"inner": 35}}, {"id": "JI7", "locationId": 14, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}, {"id": "JI21", "locationId": 42, "required": {"bhakti": 1, "gnana": 1}, "reward": {"inner": 24}}], "faceUpJourneyOuter": [{"id": "JO12", "locationId": 27, "required": {"karma": 2, "artha": 1}, "reward": {"outer": 27}}, {"id": "JO2", "locationId": 5, "required": {"karma": 1, "artha": 1}, "reward": {"outer": 24}}, {"id": "JO7", "locationId": 13, "required": {"artha": 1}, "reward": {"outer": 20}}, {"id": "JO22", "locationId": 43, "required": {"karma": 1, "artha": 2}, "reward": {"outer": 27}}], "locationCubes": {"1": "karma", "2": "gnana", "3": "artha", "4": "artha", "5": "bhakti", "6": "bhakti", "7": "gnana", "8": "gnana", "9": "karma", "10": "bhakti", "11": "artha", "12": "bhakti", "13": "karma", "14": "gnana", "15": "karma", "16": "bhakti", "17": "artha", "18": "bhakti", "19": "bhakti", "20": "karma", "21": "gnana", "22": "artha", "23": "artha", "24": "artha", "25": "artha", "26": "artha", "27": "gnana", "28": "karma", "29": "bhakti", "30": "gnana", "31": "bhakti", "32": "gnana", "33": "karma", "34": "bhakti", "35": "karma", "36": "gnana", "37": "karma", "38": "gnana", "39": "artha", "40": "gnana", "41": "karma", "42": "artha", "43": "karma", "44": "bhakti", "45": "bhakti", "46": "gnana", "47": "artha", "48": "karma"}, "locationOm": {"49": true, "50": true, "51": true, "53": true, "54": true, "55": true, "56": true, "57": true, "58": true, "59": true, "60": true}, "finalRound": false, "finalRoundStarter": null, "finalRoundEnd": null, "omTokenVictory": false, "omTokenVictor": null, "gameEvents": [{"type": "global_event_drawn", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598309193, "data": {"eventId": "global-event-43", "eventName": "Cultural Exchange", "eventEffect": "cultural_exchange"}}, {"type": "DEAL_INITIAL_CARD", "playerId": "ztxSRPPhOUIrirStAAAB", "playerName": "CosmicSeeker", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598320406, "data": {"cardType": "travel", "card": {"id": "T10", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "ztxSRPPhOUIrirStAAAB", "playerName": "CosmicSeeker", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598320406, "data": {"cardType": "travel", "card": {"id": "T35", "type": "travel", "value": 3, "vehicle": "train"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "nbHe6J6r3FLupZuIAAAH", "playerName": "me", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598320406, "data": {"cardType": "travel", "card": {"id": "T22", "type": "travel", "value": 3, "vehicle": "boat"}}}, {"type": "DEAL_INITIAL_CARD", "playerId": "nbHe6J6r3FLupZuIAAAH", "playerName": "me", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598320406, "data": {"cardType": "travel", "card": {"id": "T43", "type": "travel", "value": 2, "vehicle": "rickshaw"}}}, {"type": "omTrackAdvanced", "playerId": "ztxSRPPhOUIrirStAAAB", "playerName": "CosmicSeeker", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598322318, "data": {"newPos": 1, "count": 1}}, {"type": "move", "playerId": "ztxSRPPhOUIrirStAAAB", "playerName": "CosmicSeeker", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598322318, "data": {"path": [64, 15, 5, 52], "travelCards": ["T10"], "extraHopCards": []}}, {"type": "PICK_FACE_UP_CARDS", "playerId": "ztxSRPPhOUIrirStAAAB", "playerName": "CosmicSeeker", "roundCount": 0, "turnIndex": 0, "timestamp": 1748598325320, "data": {"cardType": "travel", "pickedCards": [{"id": "T32", "type": "travel", "value": 2, "vehicle": "motorbike"}, {"id": "T3", "type": "travel", "value": 1, "vehicle": "trek"}], "pickedFromFaceUp": true}}, {"type": "endTurn", "playerId": "ztxSRPPhOUIrirStAAAB", "playerName": "CosmicSeeker", "roundCount": 0, "turnIndex": 1, "timestamp": 1748598328321, "data": {"nextPlayerId": "nbHe6J6r3FLupZuIAAAH", "newRound": false, "roundCount": 0, "turnCount": null}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 1, "timestamp": 1748612589322, "data": {"timestamp": 1748612589322, "loadedStateRoundCount": 0, "playerCount": 2}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 1, "timestamp": 1748654047466, "data": {"timestamp": 1748654047466, "loadedStateRoundCount": 0, "playerCount": 2}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 1, "timestamp": 1749465921298, "data": {"timestamp": 1749465921298, "loadedStateRoundCount": 0, "playerCount": 2}}, {"type": "GAME_RESUMED", "playerId": "system", "playerName": "System", "roundCount": 0, "turnIndex": 1, "timestamp": 1750972374146, "data": {"timestamp": 1750972374146, "loadedStateRoundCount": 0, "playerCount": 2}}], "roundSummaries": [], "characterDeck": [{"id": "pilgrim2", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "pilgrim1", "type": "<PERSON><PERSON><PERSON>", "ability": {"gives": "bhakti", "takes": ["gnana", "artha", "karma"]}, "description": "Trade any 1 of (gnana, artha, karma) for 1 bhakti-cube"}, {"id": "merchant2", "type": "Merchant", "ability": {"gives": "artha", "takes": ["gnana", "bhakti", "karma"]}, "description": "Trade any 1 of (gnana, bhakti, karma) for 1 artha-cube"}, {"id": "engineer1", "type": "Engineer", "ability": {"gives": "karma", "takes": ["bhakti", "artha", "gnana"]}, "description": "Trade any 1 of (bhakti, artha, gnana) for 1 karma-cube"}, {"id": "professor2", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}, {"id": "professor1", "type": "Professor", "ability": {"gives": "gnana", "takes": ["bhakti", "artha", "karma"]}, "description": "Trade any 1 of (bhakti, artha, karma) for 1 gnana-cube"}], "energyCubePile": {"artha": 2, "karma": 2, "gnana": 2, "bhakti": 2}, "currentGlobalEvent": {"id": "global-event-43", "name": "Cultural Exchange", "text": "At the start of turn, you may swap locations with another player who agrees. Both gain 5 Inner points.", "effect": "cultural_exchange"}, "globalEventDeck": [{"id": "global-event-41", "name": "<PERSON><PERSON><PERSON> Dip", "text": "<PERSON><PERSON> 5 inner points if you are in West at the end of turn", "effect": "pushkar_holy_dip_end_turn_reward"}, {"id": "global-event-26", "name": "Engineer's Precision", "text": "Gain 7 outer points if an Engineer trades for karma", "effect": "engineers_precision_reward"}, {"id": "global-event-21", "name": "Heavy Haul", "text": "Use the Truck travel card for 10 outer points but lose 2 energy cubes", "effect": "heavy_haul_reward"}, {"id": "global-event-27", "name": "Frozen North", "text": "If starting in North: moves cost 2x; if moved, gain 7 inner points", "effect": "frozen_north"}, {"id": "global-event-8", "name": "Triathlon", "text": "Gain +7 outer points if travelled with 3 unique value travel cards this round", "effect": "triathlon_bonus"}, {"id": "global-event-30", "name": "Himalayan NE", "text": "Gain 5 inner points if you are in North East at the end of turn", "effect": "himalayan_ne_end_turn_reward"}, {"id": "global-event-38", "name": "Excess Baggage", "text": "Hand limit 2 this round, discard down immediately", "effect": "excess_baggage"}, {"id": "global-event-42", "name": "<PERSON><PERSON><PERSON> in the Clouds", "text": "<PERSON><PERSON> 5 inner points if you visit > 1 Airports this round", "effect": "parikrama_in_clouds_reward"}, {"id": "global-event-24", "name": "Professor's Insight", "text": "Gain 7 inner points if a Professor trades for gnana", "effect": "professors_insight_reward"}, {"id": "global-event-5", "name": "Bountiful Bhandara", "text": "Draw 2 random energy cubes; +5 outer points if any journey card collected", "effect": "draw_2_cubes_bonus_5_outer"}, {"id": "global-event-28", "name": "Solar South", "text": "Lose 2 energy cubes if you end in South", "effect": "solar_south"}, {"id": "global-event-33", "name": "<PERSON><PERSON> Caravan<PERSON>", "text": "Horse or Camel: gain +5 Outer points", "effect": "rajput_caravans_reward"}, {"id": "global-event-36", "name": "Rails and Sails", "text": "Train or Boat: gain +5 Outer points", "effect": "rails_and_sails_reward"}, {"id": "global-event-25", "name": "<PERSON><PERSON><PERSON>'s Grace", "text": "<PERSON><PERSON> 7 inner points if a <PERSON><PERSON><PERSON> trades for bhakti", "effect": "pilgrims_grace_reward"}, {"id": "global-event-4", "name": "Drought of Spirits", "text": "No inner journey cards can be collected this round", "effect": "no_inner_journey_cards"}, {"id": "global-event-1", "name": "Drizzle of Delay", "text": "Max 2 moves; ending in North or East costs 1 Artha.", "effect": "max_moves_2_and_cost_artha_north_east"}, {"id": "global-event-2", "name": "<PERSON>wal<PERSON> Distraction", "text": "All gain +5 inner pts but no cube pickup.", "effect": "gain_5_inner_no_cube_pickup"}, {"id": "global-event-10", "name": "Om Meditation", "text": "Gain 1 om token from nearest jyotirlinga in your current region", "effect": "om_meditation"}, {"id": "global-event-9", "name": "Riots", "text": "Discard 1 energy cube of each type if > 1. Also discard 1 om token in the jyotirlinga of your current region if > 1", "effect": "riots_discard"}, {"id": "global-event-32", "name": "Eco Trail", "text": "Cycle or Trek: gain +5 Inner points", "effect": "eco_trail_reward"}, {"id": "global-event-3", "name": "<PERSON><PERSON>", "text": "Visit any <PERSON><PERSON><PERSON><PERSON><PERSON> for 7 inner pts; skip for 1 bonus cube.", "effect": "jyotirlinga_7_inner_or_bonus_cube"}, {"id": "global-event-37", "name": "Central Heart", "text": "<PERSON>ain 5 inner points if you are in Central at the end of turn", "effect": "central_heart_end_turn_reward"}, {"id": "global-event-34", "name": "Urban Ride", "text": "Motorbike or Rickshaw: gain +5 Outer points", "effect": "urban_ride_reward"}, {"id": "global-event-7", "name": "Election Campaigns", "text": "All trade yield 2x. No travel allowed this round", "effect": "double_trade_no_travel"}, {"id": "global-event-23", "name": "Merchant's Midas", "text": "Gain 7 outer points if a merchant trades for artha", "effect": "merchants_midas_reward"}, {"id": "global-event-40", "name": "Spirit of Seva", "text": "Leader on each track donates 3 points to player with lowest score on that track", "effect": "spirit_of_seva"}, {"id": "global-event-39", "name": "Heritage Site Renovations", "text": "No outer journey cards can be collected this round", "effect": "no_outer_journey_cards"}, {"id": "global-event-35", "name": "Road Warriors", "text": "Car or Bus: gain +5 Outer points", "effect": "road_warriors_reward"}, {"id": "global-event-6", "name": "Turbulent Skies", "text": "No airport travel this round", "effect": "no_airport_travel"}], "globalEventDiscard": [], "nameMode": false}