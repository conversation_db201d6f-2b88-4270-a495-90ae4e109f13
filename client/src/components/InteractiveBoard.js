import React, { useState, useRef, useEffect, useCallback } from 'react';
import { nodes, edges } from '../boardLayout';
import MovementPanel from './MovementPanel';
import CompactPlayerMat from './CompactPlayerMat.jsx';
import CompactCards from './CompactCards.jsx';
import CompactJourneyCards from './CompactJourneyCards.jsx';
import JourneyCardModal from './JourneyCardModal';
import TravelCardSelection from './TravelCardSelection';
import EnergyCubeSelection from './EnergyCubeSelection';
import CardPickAnimation from './CardPickAnimation';
import endTurnSound from '../assets/sounds/end_turn.mp3';
import busBellSound from '../assets/sounds/bus_bell.mp3';
import omSound from '../assets/sounds/om.mp3';
import SpiritOfSevaModal from './SpiritOfSevaModal';

// A simple mapping for energy cube colors
const energyColors = {
  artha: 'var(--artha-color)',  // orange
  karma: 'var(--karma-color)',  // green
  gnana: 'var(--gnana-color)',  // blue
  bhakti: 'var(--bhakti-color)'   // purple
};

function InteractiveBoard({ socket, gameState, winner }) {
  // Add debugging log
  console.log('InteractiveBoard rendering with:', {
    socketId: socket?.id,
    turnIndex: gameState?.turnIndex,
    playersLength: gameState?.players?.length,
    currentPlayerExists: gameState?.players && gameState?.turnIndex !== undefined &&
      gameState.turnIndex >= 0 && gameState.turnIndex < gameState.players.length
  });
  const svgRef = useRef(null);
  const fullScreenSvgRef = useRef(null);
  const { players, turnIndex, locationCubes, locationOm } = gameState;
  const currentPlayer = players && turnIndex !== undefined && turnIndex >= 0 && turnIndex < players.length
    ? players[turnIndex]
    : null;

  // Local state to track selection and allowed destinations.
  const [selected, setSelected] = useState(false);
  const [allowedDestinations, setAllowedDestinations] = useState([]);
  // Optimistic update state for the current player's position.
  const [currentPlayerPos, setCurrentPlayerPos] = useState(currentPlayer?.position || 0);
  // Optimistically track the current player's hand.
  const [playerHand, setPlayerHand] = useState(currentPlayer?.hand || []);

  // Animation state for player movement
  const [animatingMovement, setAnimatingMovement] = useState(false);
  const [animationPath, setAnimationPath] = useState([]);
  const [animationStep, setAnimationStep] = useState(0);
  const [animationPosition, setAnimationPosition] = useState(null);
  const [animatingPlayerId, setAnimatingPlayerId] = useState(null);

  // Tracking for custom movement
  const [customMovement, setCustomMovement] = useState(false);
  const [movementPoints, setMovementPoints] = useState(0);

  // Full-screen mode state - now controlled only by the browser's fullscreen state
  const [isFullScreen, setIsFullScreen] = useState(false);
  // State to track browser fullscreen mode
  const [isBrowserFullScreen, setIsBrowserFullScreen] = useState(false);
  // Label visibility state
  const [hideLabels, setHideLabels] = useState(false);
  // Name mode state (from server)
  const [nameMode, setNameMode] = useState(gameState.nameMode || false);

  // Zoom and pan controls for full-screen mode
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // Track if we're in triathlon mode
  const [isTriathlonMovement, setIsTriathlonMovement] = useState(false);

  // Cache for 6-hop paths
  const [sixHopPaths, setSixHopPaths] = useState(new Map());

  // Journey card modal state
  const [journeyModal, setJourneyModal] = useState(null);

  // Add sound enabled state
  const [soundEnabled, setSoundEnabled] = useState(() => {
    // Get saved preference from localStorage, default to true if not set
    return localStorage.getItem('soundEnabled') !== 'false';
  });

  // Track previous locationOm state to detect changes
  const [prevLocationOm, setPrevLocationOm] = useState({});

  // Animation state for card picking
  const [cardPickAnimation, setCardPickAnimation] = useState({
    active: false,
    cards: [],
    sourcePosition: { x: 0, y: 0 },
    targetPosition: { x: 0, y: 0 }
  });

  // Add state for sidebar visibility in fullscreen mode
  const [hideSidebars, setHideSidebars] = useState(false);

  // Travel card selection state
  const [showTravelCardSelection, setShowTravelCardSelection] = useState(false);
  const [selectedTravelCards, setSelectedTravelCards] = useState([]);

  // Energy cube selection state for Heavy Haul event
  const [showEnergyCubeSelection, setShowEnergyCubeSelection] = useState(false);
  const [pendingTravelCardIds, setPendingTravelCardIds] = useState(null);
  const [movePathCache, setMovePathCache] = useState(null);

  // Add state for the new selection modals
  const [showRegionCubeSelection, setShowRegionCubeSelection] = useState(false);
  const [showRegionCardSelection, setShowRegionCardSelection] = useState(false);
  const [regionSelectionDetails, setRegionSelectionDetails] = useState(null);
  const [spiritOfSevaData, setSpiritOfSevaData] = useState(null);

  // State to toggle Om Turn Track full-screen view
  const [showOmTrack, setShowOmTrack] = useState(false);

  // Add socket event listeners for the new selection requests
  useEffect(() => {
    // Listen for card pick animation events from other players
    socket.on('cardPickAnimation', (data) => {
      try {
        // Only show animation if it's not our own action (we already see our own animation)
        if (!data || !data.playerId || data.playerId === socket.id) return;
        if (!data.cards || !Array.isArray(data.cards) || data.cards.length === 0) return;

        // Validate card data to prevent errors
        const validCards = data.cards.filter(card => card && typeof card === 'object' && card.id);
        if (validCards.length === 0) return;

        // Find the face-up cards container to calculate positions if needed
        const faceUpCardsContainer = document.querySelector('.compact-cards-container');
        if (!faceUpCardsContainer) return;

        // Find the player's mat for the target position
        const playerMatRef = document.querySelector(`.compact-player-mat[data-player-id="${data.playerId}"]`);
        if (!playerMatRef) return;

        const playerMatRect = playerMatRef.getBoundingClientRect();
        const targetPosition = {
          x: playerMatRect.left + playerMatRect.width / 2,
          y: playerMatRect.top + playerMatRect.height / 2
        };

        // Create a map of card positions
        const positionsMap = {};

        // If we have card positions from the server, use them
        if (data.cardPositions && typeof data.cardPositions === 'object') {
          // Process card positions from server
          Object.keys(data.cardPositions).forEach(cardId => {
            // For server data, we just need to calculate actual positions
            const containerRect = faceUpCardsContainer.getBoundingClientRect();
            positionsMap[cardId] = {
              x: containerRect.left + containerRect.width / 3,
              y: containerRect.top + containerRect.height / 2
            };
          });
        }

        // If we don't have positions for all cards, add fallback positions
        validCards.forEach(card => {
          if (!positionsMap[card.id]) {
            // Fallback: use container center
            const containerRect = faceUpCardsContainer.getBoundingClientRect();
            positionsMap[card.id] = {
              x: containerRect.left + containerRect.width / 2,
              y: containerRect.top + containerRect.height / 2
            };
          }
        });

        // Set animation state
        setCardPickAnimation({
          active: true,
          cards: validCards,
          cardPositions: positionsMap,
          targetPosition
        });
      } catch (error) {
        console.error('Error processing card animation data:', error);
      }
    });

    socket.on('initiateTravelCardSelection', () => {
      setShowTravelCardSelection(true);
    });

    // Listen for Heavy Haul cube selection request
    socket.on('needsHeavyHaulCubeSelection', (data) => {
      setShowEnergyCubeSelection(true);
      if (data && data.travelCardIds) {
        setPendingTravelCardIds(data.travelCardIds);
      }
    });

    // Listen for region-based energy cube selection request
    socket.on('needsEnergyCubeSelection', (data) => {
      setShowRegionCubeSelection(true);
      setRegionSelectionDetails(data);
    });

    // Listen for region-based travel card selection request
    socket.on('needsTravelCardSelection', (data) => {
      console.log('Received needsTravelCardSelection event:', data);
      setShowRegionCardSelection(true);
      setRegionSelectionDetails(data);
    });

    // Listen for player movement to animate it
    socket.on('playerMoved', (data) => {
      if (data && data.path) {
        // Play bus bell sound for movement (if sound is enabled)
        // Only play sound for other players' movements, not our own
        if (soundEnabled && data.playerId !== socket.id) {
          const audio = new Audio(busBellSound);
          audio.play();
        }

        // Animate the player movement for any player
        startMovementAnimation(data.path, null, data.playerId);
      }
    });

    socket.on('showSpiritOfSevaModal', (data) => {
      console.log('Received Spirit of Seva data:', data);
      setSpiritOfSevaData(data);
    });

    return () => {
      socket.off('initiateTravelCardSelection');
      socket.off('needsHeavyHaulCubeSelection');
      socket.off('needsEnergyCubeSelection');
      socket.off('needsTravelCardSelection');
      socket.off('playerMoved');
      socket.off('showSpiritOfSevaModal');
    };
  }, [socket, currentPlayer?.id, soundEnabled]);

  // Helper function to check if the current player is the user
  const isCurrentPlayer = useCallback(() => {
    // Only true if the client's socket ID matches the player at the current turn index
    return (
      socket && 
      gameState.players && 
      gameState.turnIndex !== undefined && 
      gameState.turnIndex >= 0 && 
      gameState.turnIndex < gameState.players.length && 
      gameState.players[gameState.turnIndex]?.id === socket.id
    );
  }, [socket, gameState]);

  // Handle movement when player clicks on a destination
  const makeMove = (destinationId) => {
    // Don't allow move if not selected and if not our turn
    if (!allowedDestinations.includes(destinationId) || !isCurrentPlayer()) {
      return;
    }

    // Find a valid path to the destination
    const path = findShortestPath(currentPlayerPos, destinationId);
    if (!path) return;

    // Check if we're using a truck card during Heavy Haul event
    const isTruckCard = selectedTravelCards.some(card => card.vehicle === 'truck');
    const isHeavyHaulEvent = gameState.currentGlobalEvent?.effect === 'heavy_haul_reward';

    // If we have truck card + heavy haul event and the energy cube selection hasn't appeared yet
    if (isHeavyHaulEvent && isTruckCard && currentPlayer.energyCubes.length >= 3 && !showEnergyCubeSelection) {
      // Store travel card IDs and path for later
      setPendingTravelCardIds(selectedTravelCards.map(card => card.id));

      // Show energy cube selection before move
      setShowEnergyCubeSelection(true);

      // Store data for the move
      setMovePathCache(path);
      return;
    }

    // Start animation before sending the move to the server
    startMovementAnimation(path, () => {
      // Send the move to the server
      socket.emit('movePlayer', {
        path: path,
        travelCardIds: selectedTravelCards.map(card => card.id),
        isTriathlon: isTriathlonMovement
      });

      // Reset UI state after move
      setSelectedTravelCards([]);
      setAllowedDestinations([]);
      setSelected(false);

      // If this was a triathlon move, reset the flag
      if (isTriathlonMovement) {
        setIsTriathlonMovement(false);
      }
    });
  };

  // Function to animate player movement along a path
  const startMovementAnimation = (path, onComplete, playerId = null) => {
    if (!path || path.length <= 1) {
      if (onComplete) onComplete();
      return;
    }

    // Start animation
    setAnimatingMovement(true);
    setAnimationPath(path);
    setAnimationStep(0);
    setAnimationPosition(path[0]);
    setAnimatingPlayerId(playerId);

    // We'll handle the animation steps with a timer
    const animateStep = () => {
      setAnimationStep(prev => {
        const nextStep = prev + 1;

        if (nextStep < path.length) {
          // Move to next position in path
          setAnimationPosition(path[nextStep]);
          return nextStep;
        } else {
          // Animation complete
          setAnimatingMovement(false);
          setAnimationPath([]);
          setAnimatingPlayerId(null);
          if (onComplete) onComplete();
          return 0;
        }
      });
    };

    // Set up animation interval - move every 300ms
    const animationInterval = setInterval(() => {
      animateStep();
    }, 300);

    // Clean up interval when animation is done
    setTimeout(() => {
      clearInterval(animationInterval);
      setAnimatingMovement(false);
      setAnimatingPlayerId(null);
      if (onComplete) onComplete();
    }, 300 * path.length);
  };

  // Handle energy cube selection for Heavy Haul
  const handleEnergyCubeSelection = (selectedCubes) => {
    setShowEnergyCubeSelection(false);

    // Send the selected cubes to the server
    socket.emit('selectEnergyCubesForHeavyHaul', {
      selectedCubes: selectedCubes,
      travelCardIds: pendingTravelCardIds
    });

    // If we had cached a move path, execute the move now
    if (movePathCache && movePathCache.length > 0) {
      // Start animation before sending the move to the server
      startMovementAnimation(movePathCache, () => {
        // Send the move to the server
        socket.emit('movePlayer', {
          path: movePathCache,
          travelCardIds: pendingTravelCardIds,
          isTriathlon: isTriathlonMovement
        });

        // Reset UI state after move
        setSelectedTravelCards([]);
        setAllowedDestinations([]);
        setSelected(false);
        setMovePathCache(null);

        // If this was a triathlon move, reset the flag
        if (isTriathlonMovement) {
          setIsTriathlonMovement(false);
        }
      });
    }

    // Clear pending data
    setPendingTravelCardIds(null);
  };

  // Handle selected travel cards from the travel card selection modal
  const handleTravelCardSelection = (selectedCards) => {
    setShowTravelCardSelection(false);
    setSelectedTravelCards(selectedCards);

    // Calculate exact hop count from selected cards
    const exactHopCount = selectedCards.reduce((sum, card) => sum + card.value, 0);
    let effectiveHopCount = exactHopCount;

    // Get the current player's region
    const currentPlayerRegion = getNodeRegion(currentPlayerPos);
    console.log('currentPlayerRegion', currentPlayerRegion);

    // Check if Himalayan Northeast global event is active and player is in Northeast region
    const isHimalayaNEEvent = gameState.currentGlobalEvent?.effect === 'himalayan_ne' ||
                             gameState.currentGlobalEvent?.effect === 'himalayan_ne_end_turn_reward';

    // Check if Frozen North global event is active and player is in North region
    const isFrozenNorthEvent = gameState.currentGlobalEvent?.effect === 'frozen_north' ||
                              gameState.currentGlobalEvent?.effect === 'frozen_north_end_turn_reward';

    if (isHimalayaNEEvent && currentPlayerRegion === 'northeast') {
      // For Himalayan NE event, show locations at half the distance (rounded down)
      // The server-side effect doubles the movement cost, so we show half the distance
      effectiveHopCount = Math.floor(exactHopCount / 2);
      console.log(`Applied Himalayan NE effect: Showing locations at ${effectiveHopCount} hops away (half the card value ${exactHopCount})`);
    } else if (isFrozenNorthEvent && currentPlayerRegion === 'north') {
      // For Frozen North event, show locations at half the distance (rounded down)
      // The server-side effect doubles the movement cost, so we show half the distance
      effectiveHopCount = Math.floor(exactHopCount / 2);
      console.log(`Applied Frozen North effect: Showing locations at ${effectiveHopCount} hops away (half the card value ${exactHopCount})`);
    }

    // Find locations exactly the effective hop count away
    const exactDestinations = getReachableLocationsExactHops(currentPlayerPos, effectiveHopCount);

    setAllowedDestinations(exactDestinations);
    setSelected(true);

    // Check if Heavy Haul event is active and we're using a truck card
    const isTruckCard = selectedCards.some(card => card.vehicle === 'truck');
    const isHeavyHaulEvent = gameState.currentGlobalEvent?.effect === 'heavy_haul_reward';

    if (isHeavyHaulEvent && isTruckCard && currentPlayer.energyCubes.length >= 3) {
      // Store the travel card IDs for later use
      setPendingTravelCardIds(selectedCards.map(card => card.id));
      // Show energy cube selection modal after a short delay
      setTimeout(() => {
        setShowEnergyCubeSelection(true);
      }, 500);
    }
  };

  // Sync external game state changes for position & hand.
  useEffect(() => {
    if (currentPlayer?.position !== undefined) {
      setCurrentPlayerPos(currentPlayer.position);
    }
  }, [currentPlayer?.position]);

  useEffect(() => {
    if (currentPlayer?.hand) {
      setPlayerHand(currentPlayer.hand);
    }
  }, [currentPlayer?.hand]);

  // Add keyboard listener for Escape key to exit full-screen mode
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && isFullScreen) {
        // If there's a journey modal open in fullscreen, preserve it for after exit
        if (journeyModal) {
          // We're already showing the modal in fullscreen, so just clear it and let it reappear
          setJourneyModal(null);
        } else {
          // Check if there's a pending modal we need to clear to prevent duplicates
          localStorage.removeItem('pendingJourneyModal');
        }
        setIsFullScreen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullScreen, journeyModal]);

  // Add this effect to handle the pendingJourneyModal when fullscreen mode changes
  useEffect(() => {
    // When exiting fullscreen mode, check if there's a pending journey modal
    if (!isFullScreen) {
      // Only process pending modal if no modal is currently showing
      if (!journeyModal) {
        const pendingModal = localStorage.getItem('pendingJourneyModal');
        if (pendingModal) {
          try {
            const modalData = JSON.parse(pendingModal);
            // Slight delay to ensure fullscreen exit is complete
            setTimeout(() => {
              setJourneyModal(modalData);
              localStorage.removeItem('pendingJourneyModal');
            }, 50);
          } catch (error) {
            console.error('Error parsing pendingJourneyModal:', error);
            localStorage.removeItem('pendingJourneyModal');
          }
        }
      } else {
        // If a modal is already showing, clear any pending modal to prevent duplicates
        localStorage.removeItem('pendingJourneyModal');
      }
    }
  }, [isFullScreen, journeyModal]);

  // Reset zoom and pan when entering full-screen mode
  useEffect(() => {
    if (isFullScreen) {
      setZoom(1);
      setPan({ x: -250, y: 0 }); // Set initial pan to shift board left to avoid overlap with journey cards
    }
  }, [isFullScreen]);

  // Handle zoom in/out
  const handleZoomIn = () => {
    setZoom(prevZoom => Math.min(prevZoom + 0.2, 3));
  };

  const handleZoomOut = () => {
    setZoom(prevZoom => Math.max(prevZoom - 0.2, 0.5));
  };

  // Handle nameMode toggle
  const handleNameModeToggle = () => {
    socket.emit('toggleNameMode');
  };

  // Handle pan/drag operations
  const handleMouseDown = (e) => {
    if (e.button === 0) { // Left click only
      setIsDragging(true);
      setDragStart({
        x: e.clientX - pan.x,
        y: e.clientY - pan.y
      });
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // Helper: get all neighbors of a node (locations connected by edges)
  const getNeighbors = (nodeId) => {
    const neighbors = new Set();

    // Get direct connections from edges
    edges.forEach((edge) => {
      if (edge.from === nodeId) neighbors.add(edge.to);
      if (edge.to === nodeId) neighbors.add(edge.from);
    });

    // If current location is an airport, add connections to all other airports
    const location = gameState.locations?.find(loc => loc.id === nodeId);
    if (location && location.journeyType === 'Airport') {
      gameState.locations?.forEach(loc => {
        if (loc.journeyType === 'Airport' && loc.id !== nodeId) {
          neighbors.add(loc.id);
        }
      });
    }

    return Array.from(neighbors);
  };

  // Helper: finds locations reachable in EXACTLY "steps" hops with airport rules
  const getReachableLocations = (start, steps) => {
    // Early return for 0 steps
    if (steps <= 0) return [];

    // Store all reachable locations at EXACTLY steps hops away
    const result = new Set();

    // Track distances using a map
    const distances = new Map();
    distances.set(start, 0);

    // BFS queue
    const queue = [start];
    while (queue.length > 0) {
      const current = queue.shift();
      const distance = distances.get(current);

      // If we're exactly at our target step count, add to results
      if (distance === steps) {
        result.add(current);
        // Don't continue exploring from this node
        continue;
      }

      // If we've gone too far, stop exploring this path
      if (distance > steps) continue;

      // Get neighbors
      const neighbors = getNeighbors(current);

      for (const neighbor of neighbors) {
        // Skip if we've already found a shorter or equal path to this node
        if (distances.has(neighbor) && distances.get(neighbor) <= distance + 1) continue;

        // Get location info
        const currentLoc = gameState.locations?.find(loc => loc.id === current);
        const neighborLoc = gameState.locations?.find(loc => loc.id === neighbor);

        // Check if this is an airport-to-airport connection
        const isAirportHop = currentLoc?.journeyType === 'Airport' &&
                             neighborLoc?.journeyType === 'Airport';

        // Update distance - for all connections it's +1 hop
        distances.set(neighbor, distance + 1);

        // Add to queue to continue BFS
        queue.push(neighbor);
      }
    }

    // Special case: always include jyotirlingas
    gameState.locations?.forEach(loc => {
      if (loc.id >= 49 && loc.id <= 60) {
        // Check if this jyotirlinga is reachable with exactly steps hops
        const distance = distances.get(loc.id);
        if (distance === steps) {
          result.add(loc.id);
        }
      }
    });

    // Remove starting location from results
    result.delete(start);

    return Array.from(result);
  };

  // Modified: find the SHORTEST path with special handling for airport-to-airport travel
  const findShortestPath = (start, destination) => {
    // Special case: direct airport-to-airport travel
    const startLocation = gameState.locations?.find(loc => loc.id === start);
    const endLocation = gameState.locations?.find(loc => loc.id === destination);

    // If both start and destination are airports, return direct path
    if (startLocation?.journeyType === 'Airport' && endLocation?.journeyType === 'Airport') {
      return [start, destination]; // Direct path, counts as 1 hop
    }

    // Normal BFS path finding for non-airport-to-airport travel
    const queue = [[start]]; // Queue of paths (each path is an array of nodes)
    const visited = new Set([start]); // Keep track of visited nodes to avoid cycles

    while (queue.length > 0) {
      const path = queue.shift(); // Dequeue the first path
      const current = path[path.length - 1]; // Get the last node in the path

      if (current === destination) {
        return path; // If we've reached the destination, return the path
      }

      const neighbors = getNeighbors(current); // Get neighbors of the current node

      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          visited.add(neighbor); // Mark the neighbor as visited

          // Add the neighbor to the path and enqueue
          queue.push([...path, neighbor]);
        }
      }
    }

    return null; // If no path is found, return null
  };

  // Socket event listener for triathlon movement
  useEffect(() => {
    // Listen for triathlon movement event
    socket.on('triathlonMovement', () => {
      // Set triathlon movement mode
      setIsTriathlonMovement(true);

      // Show exactly 6-hop destinations
      const sixHopDestinations = getReachableLocationsExactHops(currentPlayerPos, 6);
      setAllowedDestinations(sixHopDestinations);
      setSelected(true);

      // Compute and cache all 6-hop paths
      const paths = new Map();
      for (const destId of sixHopDestinations) {
        const path = findPathWithExactHops(currentPlayerPos, destId, 6);
        if (path) {
          paths.set(destId, path);
        }
      }
      setSixHopPaths(paths);
    });

    return () => {
      socket.off('triathlonMovement');
    };
  }, [socket, currentPlayerPos]);

  // Helper: finds locations reachable in EXACTLY "steps" hops with NO LOOPS
  const getReachableLocationsExactHops = (start, exactHops) => {
    // Early return for 0 steps
    if (exactHops <= 0) return [];

    // Store all reachable locations at EXACTLY steps hops away
    const result = new Set();

    // Track path to avoid loops
    const findExactHopsRecursive = (current, remainingHops, visited = new Set()) => {
      // Mark current node as visited in this path
      visited.add(current);

      // If we've reached our target hop count, add to results
      if (remainingHops === 0) {
        if (current !== start) { // Don't include start node in results
          result.add(current);
        }
        return;
      }

      // Get neighbors
      const neighbors = getNeighbors(current);

      for (const neighbor of neighbors) {
        // Skip if we've already visited this node in this path (no loops)
        if (visited.has(neighbor)) continue;

        // Continue DFS with one less hop
        findExactHopsRecursive(neighbor, remainingHops - 1, new Set(visited));
      }
    };

    // Start DFS from the start node
    findExactHopsRecursive(start, exactHops);

    return Array.from(result);
  };

  // Find a path with EXACTLY the specified number of hops (no more, no less) with NO LOOPS
  const findPathWithExactHops = (start, destination, exactHops) => {
    // If we need 0 hops and we're already at the destination, return just the start
    if (exactHops === 0 && start === destination) {
      return [start];
    }

    // Track path to avoid loops
    let foundPath = null;

    const findExactHopsPathRecursive = (current, remainingHops, path = []) => {
      // If we already found a path, no need to continue searching
      if (foundPath) return;

      // Current path including this node
      const currentPath = [...path, current];

      // If we've reached our target hop count, check if we're at the destination
      if (remainingHops === 0) {
        if (current === destination) {
          foundPath = currentPath;
        }
        return;
      }

      // Get neighbors
      const neighbors = getNeighbors(current);

      for (const neighbor of neighbors) {
        // Skip if we've already visited this node in this path (no loops)
        if (path.includes(neighbor)) continue;

        // Continue DFS with one less hop
        findExactHopsPathRecursive(neighbor, remainingHops - 1, currentPath);
      }
    };

    // Start DFS from the start node
    findExactHopsPathRecursive(start, exactHops, []);

    return foundPath;
  };

  // Clicking on the player's piece toggles selection and computes allowed destination nodes
  // based on all travel cards in hand, respecting normal hops and the special airport rule.
  const handlePlayerClick = () => {
    // Double-check that it's still this player's turn
    if (!isCurrentPlayer()) {
      console.log('Not your turn anymore! Current game state:', {
        currentPlayerId: currentPlayer?.id,
        socketId: socket?.id,
        turnIndex: gameState.turnIndex,
        turnPlayerId: gameState.players[gameState.turnIndex]?.id,
        isMatch: currentPlayer?.id === gameState.players[gameState.turnIndex]?.id,
        omTrack: gameState.omTrack
      });
      return;
    }
    
    console.log('Your turn confirmed. Current game state:', {
      currentPlayerId: currentPlayer?.id,
      socketId: socket?.id, 
      turnIndex: gameState.turnIndex,
      turnPlayerId: gameState.players[gameState.turnIndex]?.id,
      isMatch: currentPlayer?.id === gameState.players[gameState.turnIndex]?.id,
      omTrack: gameState.omTrack
    });

    if (!selected) {
      if (selectedTravelCards.length > 0) {
        // If we already have selected travel cards (from travel card selection modal)
        const exactHopCount = selectedTravelCards.reduce((sum, card) => sum + card.value, 0);
        let effectiveHopCount = exactHopCount;

        // Get the current player's region
        const currentPlayerRegion = getNodeRegion(currentPlayerPos);

        // Check if Himalayan Northeast global event is active and player is in Northeast region
        const isHimalayaNEEvent = gameState.currentGlobalEvent?.effect === 'himalayan_ne' ||
                                 gameState.currentGlobalEvent?.effect === 'himalayan_ne_end_turn_reward';

        // Check if Frozen North global event is active and player is in North region
        const isFrozenNorthEvent = gameState.currentGlobalEvent?.effect === 'frozen_north' ||
                                  gameState.currentGlobalEvent?.effect === 'frozen_north_end_turn_reward';

        if (isHimalayaNEEvent && currentPlayerRegion === 'northeast') {
          // For Himalayan NE event, show locations at half the distance (rounded down)
          // The server-side effect doubles the movement cost, so we show half the distance
          effectiveHopCount = Math.floor(exactHopCount / 2);
          console.log(`Applied Himalayan NE effect: Showing locations at ${effectiveHopCount} hops away (half the card value ${exactHopCount})`);
        } else if (isFrozenNorthEvent && currentPlayerRegion === 'north') {
          // For Frozen North event, show locations at half the distance (rounded down)
          // The server-side effect doubles the movement cost, so we show half the distance
          effectiveHopCount = Math.floor(exactHopCount / 2);
          console.log(`Applied Frozen North effect: Showing locations at ${effectiveHopCount} hops away (half the card value ${exactHopCount})`);
        }

        const exactDestinations = getReachableLocationsExactHops(currentPlayerPos, effectiveHopCount);

        setAllowedDestinations(exactDestinations);
        setSelected(true);
        return;
      }

      // Traditional flow (without pre-selected travel cards) - not used anymore with the new UI
      const travelCards = currentPlayer.hand.filter(
        (card) => card.value && !isNaN(card.value)
      );

      // If no travel cards, can't move
      if (travelCards.length === 0) return;

      // Show travel card selection modal instead of calculating all possible destinations
      setShowTravelCardSelection(true);
    } else {
      // Deselect and clear selection state
      setSelected(false);
      setAllowedDestinations([]);
      setCustomMovement(false);
      setSelectedTravelCards([]);
    }
  };

  // When an allowed destination is selected, determine which travel card(s) can be used.
  // Compute the SHORTEST path with that hop count and then emit the move event.
  const handleDestinationClick = (destId) => {
    if (!allowedDestinations.includes(destId)) return;

    // Special handling for triathlon movement
    if (isTriathlonMovement) {
      // Get the pre-computed 6-hop path
      const path = sixHopPaths.get(destId);

      if (path) {
        // Find travel cards with values 1, 2, and 3
        const travelCards = currentPlayer.hand.filter(
          (card) => card.value && (card.value === 1 || card.value === 2 || card.value === 3)
        );

        // Get exactly one card of each required value
        const card1 = travelCards.find(card => card.value === 1);
        const card2 = travelCards.find(card => card.value === 2);
        const card3 = travelCards.find(card => card.value === 3);

        // Make sure we have all three required cards
        if (card1 && card2 && card3) {
          const cardIds = [card1.id, card2.id, card3.id];

          // Optimistically update player's position
          setCurrentPlayerPos(destId);
          // Remove the used travel cards from the optimistic hand
          setPlayerHand(playerHand.filter(c => !cardIds.includes(c.id)));
          // Emit move event with the path and card IDs
          socket.emit('movePlayer', {
            path: path,
            travelCardIds: cardIds,
            extraHopCount: 0,
            isTriathlon: true
          });
          setSelected(false);
          setAllowedDestinations([]);
          setIsTriathlonMovement(false);
          setSixHopPaths(new Map());
          return;
        }
      }

      // If we got here, something went wrong with the triathlon movement
      console.log('Error with triathlon movement - path or cards not found');
      setSelected(false);
      setAllowedDestinations([]);
      setIsTriathlonMovement(false);
      setSixHopPaths(new Map());
      return;
    }

    // If we have selected travel cards (from the travel card selection)
    if (selectedTravelCards.length > 0) {
      // Calculate exact hop count from selected cards
      const exactHopCount = selectedTravelCards.reduce((sum, card) => sum + card.value, 0);
      let effectiveHopCount = exactHopCount;

      // Get the current player's region
      const currentPlayerRegion = getNodeRegion(currentPlayerPos);

      // Check if Himalayan Northeast global event is active and player is in Northeast region
      const isHimalayaNEEvent = gameState.currentGlobalEvent?.effect === 'himalayan_ne' ||
                               gameState.currentGlobalEvent?.effect === 'himalayan_ne_end_turn_reward';

      // Check if Frozen North global event is active and player is in North region
      const isFrozenNorthEvent = gameState.currentGlobalEvent?.effect === 'frozen_north' ||
                                gameState.currentGlobalEvent?.effect === 'frozen_north_end_turn_reward';

      if (isHimalayaNEEvent && currentPlayerRegion === 'northeast') {
        // For Himalayan NE event, show locations at half the distance (rounded down)
        // The server-side effect doubles the movement cost, so we show half the distance
        effectiveHopCount = Math.floor(exactHopCount / 2);
        console.log(`Applied Himalayan NE effect: Finding path with ${effectiveHopCount} hops (half the card value ${exactHopCount})`);
      } else if (isFrozenNorthEvent && currentPlayerRegion === 'north') {
        // For Frozen North event, show locations at half the distance (rounded down)
        // The server-side effect doubles the movement cost, so we show half the distance
        effectiveHopCount = Math.floor(exactHopCount / 2);
        console.log(`Applied Frozen North effect: Finding path with ${effectiveHopCount} hops (half the card value ${exactHopCount})`);
      }

      // Find the path with exactly the right number of hops
      const path = findPathWithExactHops(currentPlayerPos, destId, effectiveHopCount);

      if (path) {
        const cardIds = selectedTravelCards.map(card => card.id);

        // Optimistically update player's position
        setCurrentPlayerPos(destId);
        // Remove the used travel cards from the optimistic hand
        setPlayerHand(playerHand.filter(c => !cardIds.includes(c.id)));
        // Emit move event with the path and card IDs
        socket.emit('movePlayer', {
          path: path,
          travelCardIds: cardIds,
          extraHopCount: 0
        });

        // Reset selection state
        setSelected(false);
        setAllowedDestinations([]);
        setSelectedTravelCards([]);
        return;
      } else {
        console.log('Error: No valid path found with exact hop count');
        return;
      }
    }

    // Legacy code for the old movement system (without pre-selecting travel cards)
    // This shouldn't be reached with the new UI, but kept for backward compatibility
    // Regular movement logic below
    // Find travel cards that can be used for this journey
    const travelCards = currentPlayer.hand.filter(
      (card) => card.value && !isNaN(card.value)
    );

    // Get location information for starting position and destination
    const startLocation = gameState.locations?.find(loc => loc.id === currentPlayerPos);
    const endLocation = gameState.locations?.find(loc => loc.id === destId);

    // Check if this is an airport-to-airport travel
    const isDirectAirportTravel = startLocation?.journeyType === 'Airport' &&
                             endLocation?.journeyType === 'Airport';

    // Check if destination is a jyotirlinga (nodes 49-60)
    const isJyotirlinga = destId >= 49 && destId <= 60;

    // For direct airport-to-airport travel, we only need a travel card with value 1
    if (isDirectAirportTravel) {
      const airportCard = travelCards.find(card => card.value === 1);
      if (airportCard) {
        // Optimistically update player's position
        setCurrentPlayerPos(destId);
        // Remove the used travel card from the optimistic hand
        setPlayerHand(playerHand.filter(c => c.id !== airportCard.id));
        // Emit move event with direct path and the travel card id
        socket.emit('movePlayer', {
          path: [currentPlayerPos, destId],
          travelCardIds: [airportCard.id],
          extraHopCount: 0
        });
        setSelected(false);
        setAllowedDestinations([]);
        return;
      }
    }

    // Find the best path to the destination
    const path = findShortestPath(currentPlayerPos, destId);

    if (path) {
      const hopCount = path.length - 1; // Number of edges in the path

      // Check if path contains at least one airport-to-airport hop
      let containsAirportHop = false;
      for (let i = 0; i < path.length - 1; i++) {
        const current = path[i];
        const next = path[i + 1];
        const currentLoc = gameState.locations?.find(loc => loc.id === current);
        const nextLoc = gameState.locations?.find(loc => loc.id === next);

        if (currentLoc?.journeyType === 'Airport' && nextLoc?.journeyType === 'Airport') {
          containsAirportHop = true;
          break;
        }
      }

      // If path contains airport hops, we need to adjust the hop count
      // since airport-to-airport counts as 1 hop regardless of distance
      let adjustedHopCount = hopCount;
      if (containsAirportHop) {
        // Count how many segments are airport-to-airport hops
        let airportHops = 0;
        let inAirportSequence = false;

        for (let i = 0; i < path.length; i++) {
          const nodeLoc = gameState.locations?.find(loc => loc.id === path[i]);
          const isAirport = nodeLoc?.journeyType === 'Airport';

          if (isAirport) {
            if (!inAirportSequence) {
              inAirportSequence = true;
            }
          } else {
            if (inAirportSequence) {
              airportHops++;
              inAirportSequence = false;
            }
          }
        }

        // End of path might also be an airport
        if (inAirportSequence) {
          airportHops++;
        }

        // Adjust hop count - each sequence of airports counts as 1 hop
        adjustedHopCount = hopCount - airportHops + 1;
      }

      // Helper function to find combinations of travel cards that sum to a target value
      const findCardCombination = (cards, targetValue) => {
        // Allow up to 4 cards for movement
        const MAX_CARDS = 4;

        // Try different numbers of cards (1 to 4)
        for (let numCards = 1; numCards <= Math.min(MAX_CARDS, cards.length); numCards++) {
          const combinations = [];

          // Helper function to generate combinations using backtracking
          const backtrack = (start, remaining, currentCombo) => {
            // Accept any valid combination that reaches the exact target value
            if (remaining === 0) {
              combinations.push([...currentCombo]);
              return;
            }

            if (currentCombo.length >= MAX_CARDS || remaining <= 0 || start >= cards.length) {
              return;
            }

            for (let i = start; i < cards.length; i++) {
              if (cards[i].value <= remaining) {
                currentCombo.push(cards[i]);
                backtrack(i + 1, remaining - cards[i].value, currentCombo);
                currentCombo.pop();
              }
            }
          };

          backtrack(0, targetValue, []);

          if (combinations.length > 0) {
            // Return the first valid combination found
            return combinations[0].map(card => card.id);
          }
        }

        return null;
      };

      // Try to find a combination of travel cards that match the adjusted hop count
      const cardIds = findCardCombination(travelCards, adjustedHopCount);

      if (cardIds) {
        // Optimistically update player's position
        setCurrentPlayerPos(destId);
        // Remove the used travel cards from the optimistic hand
        setPlayerHand(playerHand.filter(c => !cardIds.includes(c.id)));
        // Emit move event with the path and card IDs
        socket.emit('movePlayer', {
          path: path,
          travelCardIds: cardIds,
          extraHopCount: 0
        });
        setSelected(false);
        setAllowedDestinations([]);
        return;
      }
    }

    // If no valid path found with current travel cards
    console.log('No valid travel card/path found for the selected destination');
  };

  // Get region based on node ID
  const getNodeRegion = (nodeId) => {
    // Ensure nodeId is treated as a number
    nodeId = parseInt(nodeId, 10);

    // Use gameState.locations as the source of truth for region assignments
    const location = gameState.locations?.find(loc => loc.id === nodeId);

    if (location && location.region) {
      // Convert the region name to lowercase for CSS class compatibility
      return location.region.toLowerCase();
    }

    // Fallback for any locations that might not be found in the server data
    // (should not happen if server data is complete)
    const fallbackRegionMap = {
      // Jyotirlingas (special handling)
      49: 'jyotirlinga', 50: 'jyotirlinga', 51: 'jyotirlinga',
      52: 'jyotirlinga', 53: 'jyotirlinga', 54: 'jyotirlinga',
      55: 'jyotirlinga', 56: 'jyotirlinga', 57: 'jyotirlinga',
      58: 'jyotirlinga', 59: 'jyotirlinga', 60: 'jyotirlinga',

      // Airports (special handling)
      61: 'airport', 62: 'airport', 63: 'airport',
      64: 'airport', 65: 'airport', 66: 'airport',
    };

    if (fallbackRegionMap[nodeId]) {
      return fallbackRegionMap[nodeId];
    }

    // If all else fails, try to determine based on ID ranges
    if (nodeId >= 1 && nodeId <= 9) return 'north';
    if (nodeId >= 10 && nodeId <= 19) return 'west';
    if (nodeId >= 20 && nodeId <= 27) return 'south';
    if (nodeId >= 28 && nodeId <= 35) return 'central';
    if (nodeId >= 36 && nodeId <= 40) return 'east';
    if (nodeId >= 41 && nodeId <= 48) return 'northeast';

    return 'unknown';
  };

  // Get region color based on region name
  const getRegionColor = (region) => {
    const regionColors = {
      'north': 'rgba(33, 150, 243, 0.95)',
      'west': 'rgba(76, 175, 80, 0.95)',
      'south': 'rgba(255, 191, 102, 0.95)',
      'central': 'rgba(156, 39, 176, 0.95)',
      'east': '#fd7c03ef',
      'northeast': 'rgba(233, 30, 99, 0.95)',
      'jyotirlinga': 'rgba(255, 236, 130, 0.95)',
      'airport': 'rgba(103, 58, 183, 0.95)',
    };
    return regionColors[region] || '#ffffff';
  };

  // Create custom dual-layered location SVG
  const createCustomLocationSVG = (nodeId, region, isHighlighted) => {
    const regionColor = getRegionColor(region);
    
    // Create a custom shape - hexagon-like with rounded corners
    const outerPath = "M 0,-20 L 17,-10 L 17,10 L 0,20 L -17,10 L -17,-10 Z";
    const innerPath = "M 0,-15 L 12,-7 L 12,7 L 0,15 L -12,7 L -12,-7 Z";
    
    return (
      <g>
        {/* Outer layer - white */}
        <path
          d={outerPath}
          fill="white"
          stroke={isHighlighted ? '#ff5722' : '#333'}
          strokeWidth={isHighlighted ? 3 : 1.5}
          style={{
            filter: isHighlighted ? 'drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))' : 'none'
          }}
        />
        {/* Inner layer - region color */}
        <path
          d={innerPath}
          fill={regionColor}
          stroke="none"
        />
        {/* Location number - white text */}
        <text
          x="0"
          y="0"
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="14"
          fontWeight="bold"
          fill="white"
          style={{
            textShadow: '0 0 2px rgba(0, 0, 0, 0.5)'
          }}
        >
          {nodeId}
        </text>
      </g>
    );
  };

  const getNodeColor = (nodeId) => {
    // Use pure white for all nodes
    return '#ffffff';
  };

  // Function to determine if a node should pulse/animate
  const shouldHighlightNode = (nodeId) => {
    // Don't highlight current location as a valid destination
    if (nodeId === currentPlayerPos) {
      return false;
    }

    // Only highlight nodes when in selection mode
    if (!selected) return false;

    return allowedDestinations.includes(nodeId);
  };

  // Render a player piece with a nice design
  const renderPlayerPiece = (player, index) => {
    const nodeId = player.position;
    const node = nodes[nodeId];
    if (!node) return null;

    // Calculate offset for multiple players on same node
    const playerCount = players.filter(p => p.position === player.position).length;
    const playerIndex = players.filter(p => p.position === player.position).indexOf(player);
    const offsetX = playerCount > 1 ? (playerIndex - (playerCount - 1) / 2) * 20 : 0;

    const isCurrentPlayerPiece = player.id === currentPlayer.id;

    // Skip rendering the current player piece if we're animating movement for this player
    if (isCurrentPlayerPiece && animatingMovement) return null;

    return (
      <g
        key={`player-${player.id}`}
        transform={`translate(${node.x + offsetX}, ${node.y - 25})`}
      >
        <circle
          cx="0"
          cy="0"
          r="15"
          fill={isCurrentPlayerPiece ? '#ff5722' : '#999'}
          stroke="#fff"
          strokeWidth="2"
          style={{
            filter: isCurrentPlayerPiece ? 'drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))' : '',
            cursor: isCurrentPlayerPiece && !selected ? 'pointer' : 'default'
          }}
          onClick={() => isCurrentPlayerPiece && handlePlayerClick()}
        />
        <text
          x="0"
          y="0"
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="10"
          fontWeight="bold"
          fill="#fff"
        >
          {player.name.charAt(0).toUpperCase()}
        </text>
      </g>
    );
  };

  // Render the animating player piece during movement
  const renderAnimatingPlayerPiece = () => {
    if (!animatingMovement || !animationPosition) return null;

    const nodeId = animationPosition;
    const node = nodes[nodeId];
    if (!node) return null;

    // Calculate path completion percentage for progress indicator
    const progressPercent = (animationStep / (animationPath.length - 1)) * 100;

    // Find the player who is moving (default to current player if not specified)
    const movingPlayerId = animatingPlayerId || currentPlayer?.id;
    const movingPlayer = players.find(p => p.id === movingPlayerId) || currentPlayer || { name: 'Unknown' };

    // Determine player color based on turn index
    const playerIndex = players.findIndex(p => p.id === movingPlayerId);
    const playerColors = ['#ff5722', '#2196f3', '#4caf50']; // Orange, Blue, Green
    const playerColor = playerColors[playerIndex] || '#ff5722';

    return (
      <g key="animating-player">
        {/* Path indicator line showing the full path */}
        {animationPath.length > 1 && (
          <path
            d={animationPath.map((id, idx) => {
              const pathNode = nodes[id];
              return idx === 0 ? `M ${pathNode.x} ${pathNode.y}` : `L ${pathNode.x} ${pathNode.y}`;
            }).join(' ')}
            stroke={playerColor}
            strokeWidth="3"
            strokeDasharray="5,5"
            fill="none"
            opacity="0.7"
          />
        )}

        {/* Current position circle */}
        <g transform={`translate(${node.x}, ${node.y - 25})`}>
          <circle
            cx="0"
            cy="0"
            r="15"
            fill={playerColor}
            stroke="#fff"
            strokeWidth="2"
            style={{
              filter: `drop-shadow(0 0 7px ${playerColor}99)`,
              animation: 'moveAnimation 0.8s infinite'
            }}
          />
          <text
            x="0"
            y="0"
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="10"
            fontWeight="bold"
            fill="#fff"
          >
            {movingPlayer.name.charAt(0).toUpperCase()}
          </text>
        </g>
      </g>
    );
  };

  // Render energy cubes on locations
  const renderLocationCubes = () => {
    return Object.entries(locationCubes || {}).map(([nodeId, cubes]) => {
      nodeId = parseInt(nodeId, 10);
      const node = nodes[nodeId];
      if (!node || !cubes) return null;

      // Handle case where cubes is a string (single cube) or an object
      // Convert to array if it's not already
      const cubesArray = Array.isArray(cubes) ? cubes : [cubes];
      if (cubesArray.length === 0) return null;

      // If nameMode is enabled, don't render cubes outside the circle
      // They will be rendered inside the circle instead of the location number
      if (nameMode) return null;

      return (
        <g key={`cubes-${nodeId}`}>
          {cubesArray.map((cube, i) => {
            const color = energyColors[cube] || '#ccc';
            // Arrange cubes in a small circle around the node
            const angle = (i * (2 * Math.PI)) / cubesArray.length;
            const cubeRadius = 31.25; // distance from node center
            const x = node.x + cubeRadius * Math.cos(angle);
            const y = node.y + cubeRadius * Math.sin(angle);

            return (
              <rect
                key={`cube-${nodeId}-${i}`}
                x={x - 6.25}
                y={y - 6.25}
                width="12.5"
                height="12.5"
                rx="2.5"
                fill={color}
                stroke="#fff"
                strokeWidth="1"
              />
            );
          })}
        </g>
      );
    });
  };

  // Render OM tokens on locations
  const renderLocationOM = () => {
    return Object.entries(locationOm || {}).map(([nodeId, count]) => {
      nodeId = parseInt(nodeId, 10);
      const node = nodes[nodeId];

      // Make sure we have a valid node
      if (!node) return null;

      // Handle both boolean and numeric values
      let omCount;
      if (typeof count === 'boolean') {
        omCount = count === true ? 1 : 0;
      } else {
        omCount = parseInt(count, 10);
      }

      // Skip if no OM tokens
      if (isNaN(omCount) || omCount <= 0) return null;

      return (
        <g key={`om-${nodeId}`} transform={`translate(${node.x}, ${node.y + 25})`}>
          <circle
            cx="0"
            cy="0"
            r="15"
            fill="#ffd700"
            stroke="#fff"
            strokeWidth="1"
          />
          <text
            x="0"
            y="0"
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize="12.5"
            fontWeight="bold"
            fill="#000"
          >
            {omCount}
          </text>
        </g>
      );
    });
  };

  // Update soundEnabled when localStorage changes (for sync between components)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'soundEnabled') {
        setSoundEnabled(e.newValue !== 'false');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Update nameMode when gameState changes
  useEffect(() => {
    if (gameState.nameMode !== undefined) {
      setNameMode(gameState.nameMode);
    }
  }, [gameState.nameMode]);

  // Detect changes in locationOm to play sound when a player collects an OM token
  useEffect(() => {
    // Skip on first render
    if (Object.keys(prevLocationOm).length === 0) {
      setPrevLocationOm(locationOm || {});
      return;
    }

    // Check if any OM tokens were collected (removed from a location)
    let omCollectedLocation = null;
    const omCollected = Object.keys(prevLocationOm).some(locId => {
      // If the location had an OM token before but not now, or has fewer tokens now
      const prevCount = prevLocationOm[locId] || 0;
      const currentCount = locationOm[locId] || 0;
      if (prevCount > currentCount) {
        omCollectedLocation = parseInt(locId, 10);
        return true;
      }
      return false;
    });

    // If an OM token was collected and we have sound enabled
    if (omCollected && soundEnabled && currentPlayer) {
      // Only play sound if we're not the active player (we're observing another player's turn)
      if (socket.id !== currentPlayer.id) {
        // Check if the active player is at a Jyotirlinga location (nodes 49-60)
        const isAtJyotirlinga = currentPlayer.position >= 49 && currentPlayer.position <= 60;

        // Check if the OM token was collected from the active player's current location
        const collectedFromCurrentLocation = omCollectedLocation === currentPlayer.position;

        // Play the OM sound if the active player is at a Jyotirlinga and collected the OM token
        if (isAtJyotirlinga && collectedFromCurrentLocation) {
          const audio = new Audio(omSound);
          audio.play();
        }
      }
    }

    // Update previous state
    setPrevLocationOm(locationOm || {});
  }, [locationOm, soundEnabled, currentPlayer?.id, socket?.id, currentPlayer?.position]);

  // Function to handle cards being picked from face-up display
  const handleCardsPicked = (cards, cardPositions) => {
    if (!cards || cards.length === 0) return;
    if (!cardPositions || !Array.isArray(cardPositions) || cardPositions.length === 0) {
      console.warn('No valid card positions provided for animation');
      return;
    }

    // Find the current player's mat position for animation target
    const playerMatRef = document.querySelector('.compact-player-mat.current-user');
    if (!playerMatRef) {
      console.warn('Could not find current player mat for animation target');
      return;
    }

    const playerMatRect = playerMatRef.getBoundingClientRect();
    const targetPosition = {
      x: playerMatRect.left + playerMatRect.width / 2,
      y: playerMatRect.top + playerMatRect.height / 2
    };

    // Create a map of card positions by card ID
    const positionsMap = {};
    try {
      cardPositions.forEach(item => {
        if (item && item.cardId && item.position) {
          positionsMap[item.cardId] = item.position;
        }
      });
    } catch (error) {
      console.error('Error processing card positions:', error);
    }

    // Only proceed if we have at least one valid position
    if (Object.keys(positionsMap).length === 0) {
      console.warn('No valid card positions could be processed');
      return;
    }

    // Set animation state with individual card positions
    setCardPickAnimation({
      active: true,
      cards,
      cardPositions: positionsMap,
      targetPosition
    });
  };

  // Function to handle animation completion
  const handleAnimationComplete = () => {
    // Add a longer delay before removing the animation component
    // This ensures the animation completes fully before cleanup
    setTimeout(() => {
      setCardPickAnimation(prev => ({
        ...prev,
        active: false
      }));
    }, 500); // Increased to 500ms for better visibility
  };

  // Function to handle end turn with sound
  const handleEndTurn = () => {
    // Log the current state before ending turn
    console.log('End turn clicked. Current state:', {
      currentPlayer: currentPlayer ? { id: currentPlayer.id, name: currentPlayer.name, position: currentPlayer.position } : null,
      turnIndex: gameState.turnIndex,
      playersLength: gameState.players.length
    });

    if (soundEnabled) {
      const audio = new Audio(endTurnSound);
      audio.play();
    }
    socket.emit('endTurn');
  };

  // Handle region-based energy cube selection
  const handleRegionCubeSelection = (selectedCubes) => {
    setShowRegionCubeSelection(false);

    // Send the selected cubes to the server
    socket.emit('selectEnergyCubesToDiscard', {
      selectedCubes: selectedCubes
    });

    // Clear region selection details
    setRegionSelectionDetails(null);
  };

  // Handle region-based travel card selection
  const handleRegionCardSelection = (selectedCardIds) => {
    setShowRegionCardSelection(false);

    // Send the selected cards to the server
    socket.emit('selectTravelCardsToDiscard', {
      selectedCardIds: selectedCardIds
    });

    // Clear region selection details
    setRegionSelectionDetails(null);
  };

  // Function to toggle fullscreen mode using the browser's API
  const toggleFullScreen = () => {
    const element = document.documentElement;

    if (!document.fullscreenElement) {
      // Enter fullscreen mode
      if (element.requestFullscreen) {
        element.requestFullscreen().then(() => {
          setIsBrowserFullScreen(true);
          setIsFullScreen(true);
        }).catch(err => {
          console.error(`Error attempting to enable fullscreen: ${err.message}`);
        });
      }
    } else {
      // Exit fullscreen mode
      if (document.exitFullscreen) {
        document.exitFullscreen().then(() => {
          setIsBrowserFullScreen(false);
          setIsFullScreen(false);
        }).catch(err => {
          console.error(`Error attempting to exit fullscreen: ${err.message}`);
        });
      }
    }
  };

  // Listen for fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      // If user exits fullscreen via browser controls (ESC key, etc.)
      if (!document.fullscreenElement && isBrowserFullScreen) {
        setIsBrowserFullScreen(false);
        setIsFullScreen(false);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [isBrowserFullScreen]);

  // Add a proper useEffect to ensure we respond to turn index changes
  useEffect(() => {
    // Force update of local state when turn index changes
    if (gameState.turnIndex !== undefined) {
      console.log('Turn index changed to:', gameState.turnIndex);
      console.log('Current player is now:', gameState.players && gameState.turnIndex >= 0 && gameState.turnIndex < gameState.players.length ? 
        gameState.players[gameState.turnIndex]?.name : 'unknown');
      
      // Reset any movement-related UI state when the turn changes to someone else
      if (!isCurrentPlayer()) {
        setSelected(false);
        setAllowedDestinations([]);
        setCustomMovement(false);
        setSelectedTravelCards([]);
        setShowTravelCardSelection(false);
      }
      
      // Update current player position
      if (currentPlayer?.position !== undefined) {
        setCurrentPlayerPos(currentPlayer.position);
      }
    }
  }, [gameState.turnIndex, isCurrentPlayer, currentPlayer]);

  // Update the Om Turn Track display description
  <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
    <span style={{ fontSize: '12px', color: '#666' }}>
      Turn Order: Higher Position First → Top of Stack First
    </span>
    <button 
      onClick={() => setShowOmTrack(false)}
      style={{
        width: '24px',
        height: '24px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        border: 'none',
        borderRadius: '50%',
        backgroundColor: 'var(--secondary-color)',
        color: 'white',
        fontSize: '12px',
        cursor: 'pointer'
      }}
    >
      ✕
    </button>
  </div>

  // Update the debug output when turn order changes to better show the stack position
  useEffect(() => {
    // Force update of local state when turn index changes
    if (gameState.turnIndex !== undefined) {
      console.log('Turn index changed to:', gameState.turnIndex);
      console.log('Current player is now:', gameState.players && gameState.turnIndex >= 0 && gameState.turnIndex < gameState.players.length ? 
        gameState.players[gameState.turnIndex]?.name : 'unknown');
      
      // Add debugging for Om Turn Track status
      if (gameState.omTrack) {
        const turnOrderDebug = [];
        // First process higher Om spaces (going backwards)
        for (let i = 7; i >= 0; i--) {
          const stack = gameState.omTrack[i] || [];
          // Process players on this space from top of stack to bottom
          for (let j = 0; j < stack.length; j++) {
            const playerId = stack[j];
            const player = gameState.players.find(p => p.id === playerId);
            if (player) {
              turnOrderDebug.push({
                name: player.name,
                omSpace: i,
                stackPosition: j,
                isCurrentTurn: gameState.players[gameState.turnIndex]?.id === playerId
              });
            }
          }
        }
        
        console.log('Current turn order based on Om Track:', turnOrderDebug);
      }
      
      // Reset any movement-related UI state when the turn changes to someone else
      if (!isCurrentPlayer()) {
        setSelected(false);
        setAllowedDestinations([]);
        setCustomMovement(false);
        setSelectedTravelCards([]);
        setShowTravelCardSelection(false);
      }
      
      // Update current player position
      if (currentPlayer?.position !== undefined) {
        setCurrentPlayerPos(currentPlayer.position);
      }
    }
  }, [gameState.turnIndex, isCurrentPlayer, currentPlayer, gameState.omTrack, gameState.players]);

  return (
    <div className="interactive-board">
      {/* Main header */}
      <div className="board-header" style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '10px',
        background: '#f5f5f5',
        borderBottom: '1px solid #ddd'
      }}>
        <div>
          <h3>Game Board</h3>
          <p style={{ fontSize: '0.9rem', color: '#666' }}>
            Your current position: <strong>{currentPlayer ? currentPlayer.position : 'Unknown'}</strong>
          </p>
        </div>

        <div className="flex gap-sm">
          <button
            onClick={() => setHideLabels(!hideLabels)}
            className="secondary"
          >
            {hideLabels ? 'Show Labels' : 'Hide Labels'}
          </button>

          <button
            onClick={handleNameModeToggle}
            className="secondary"
          >
            {nameMode ? 'Show Numbers' : 'Show Cubes'}
          </button>

          <button
            onClick={toggleFullScreen}
            className="secondary"
          >
            <span role="img" aria-label="Full Screen">🔍</span> {isFullScreen ? 'Exit Full Screen' : 'Full Screen'}
          </button>

          {isCurrentPlayer() && (
            <>
              <button
                onClick={handlePlayerClick}
                disabled={playerHand.filter(c => c.type === 'travel').length === 0}
              >
                Move with Cards
              </button>
              <button
                className="accent"
                onClick={() => setCustomMovement(true)}
              >
                Custom Movement
              </button>
            </>
          )}
        </div>
      </div>

      {/* Board Area */}
      <div
        className="board-area"
        style={{
          position: 'relative',
          height: 'calc(100vh - 60px)',
          overflow: 'hidden'
        }}
      >
        {selected && (
          <div style={{
            padding: '10px',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            marginBottom: '10px'
          }}>
            <h3>Choose a destination:</h3>
            <p style={{ fontSize: '0.9rem', color: '#666' }}>
              Click on a highlighted location on the board to move there.
            </p>
            <button
              className="secondary"
              onClick={() => {
                setSelected(false);
                setAllowedDestinations([]);
                setCustomMovement(false);
              }}
              style={{ marginTop: '0.5rem' }}
            >
              Cancel
            </button>
          </div>
        )}

        {customMovement && (
          <MovementPanel
            socket={socket}
            currentPosition={currentPlayerPos}
            onCancel={() => setCustomMovement(false)}
          />
        )}

        {/* Normal view SVG */}
        {!isFullScreen && (
          <svg
            ref={svgRef}
            viewBox="0 0 1600 1400"
            className={`board-svg ${hideLabels ? 'hide-labels' : ''}`}
          >
            {/* Add map background image as first element */}
            <image
              href="/assets/images/map_take_2.jpg"
              width="1600"
              height="1400"
              x="0"
              y="0"
              preserveAspectRatio="xMidYMid slice"
            />

            {/* Draw edges as curved paths instead of straight lines */}
            {edges.map((edge) => {
              const fromNode = nodes[edge.from];
              const toNode = nodes[edge.to];
              if (!fromNode || !toNode) return null;

              // Calculate midpoint
              const midX = (fromNode.x + toNode.x) / 2;
              const midY = (fromNode.y + toNode.y) / 2;

              // Calculate perpendicular offset for control point
              // Create a perpendicular vector for the curve
              const dx = toNode.x - fromNode.x;
              const dy = toNode.y - fromNode.y;
              const distance = Math.sqrt(dx * dx + dy * dy);

              // Scale the curve amount based on distance
              const curveAmount = Math.min(distance * 0.2, 40);

              // Create perpendicular vector
              const perpX = -dy / distance * curveAmount;
              const perpY = dx / distance * curveAmount;

              // Control point is midpoint plus perpendicular offset
              const controlX = midX + perpX;
              const controlY = midY + perpY;

              // Create SVG path command for quadratic bezier curve
              const pathData = `M ${fromNode.x} ${fromNode.y} Q ${controlX} ${controlY} ${toNode.x} ${toNode.y}`;

              return (
                <path
                  key={`${edge.from}-${edge.to}`}
                  d={pathData}
                  fill="none"
                  stroke="#ffffff"
                  strokeWidth="3"
                  strokeLinecap="round"
                  opacity="0.9"
                />
              );
            })}

            {/* Draw nodes */}
            {Object.entries(nodes).map(([nodeId, node]) => {
              nodeId = parseInt(nodeId, 10);
              const isHighlighted = shouldHighlightNode(nodeId);
              const nodeColor = getNodeColor(nodeId);
              const region = getNodeRegion(nodeId);
              const location = gameState.locations?.find(l => l.id === nodeId);
              const locationName = location ? location.name : '';

              // Determine if it's a special node type
              const isJyotirlinga = nodeId >= 49 && nodeId <= 60;
              const isAirport = location?.journeyType === 'Airport' || (nodeId >= 61 && nodeId <= 66);

              // Advanced label positioning system with consistent pattern by region
              // This ensures labels are always positioned in a way that minimizes overlap
              let labelOffsetX = 0;
              let labelOffsetY = 40; // Default distance below node

              // Calculate optimal label position based on region and surrounding nodes
              switch(region) {
                case 'north':
                  labelOffsetY = -45; // Further above node
                  break;
                case 'south':
                  labelOffsetY = 45; // Further below node
                  break;
                case 'west':
                  labelOffsetX = -70; // Further left of node
                  labelOffsetY = 0;
                  break;
                case 'east':
                  labelOffsetX = 70; // Further right of node
                  labelOffsetY = 0;
                  break;
                case 'central':
                  // Alternate left/right based on node ID parity to reduce overlap
                  labelOffsetX = nodeId % 2 === 0 ? 70 : -70;
                  labelOffsetY = 0;
                  break;
                case 'northeast':
                  labelOffsetY = -45; // Further above node
                  break;
                case 'airport':
                  // For Airports, position consistently below
                  labelOffsetY = 60;
                  break;
              }

              // Special case for node 11 which needs extra offset
              if (nodeId === 11) {
                labelOffsetX = -80;
              }

              const labelX = node.x + labelOffsetX;
              const labelY = node.y + labelOffsetY;

              // Calculate the width needed for the text based on its length
              const textLength = locationName.length;
              const labelWidth = Math.max(120, Math.min(textLength * 7.5, 180)); // Scale width based on text length

              return (
                <g key={`node-${nodeId}`} className="node-group">
                  {/* For Jyotirlingas, use the jyotirlinga icon image */}
                  {isJyotirlinga && (
                    <g>
                      {/* Background circle for highlighting */}
                      <circle
                        cx={node.x}
                        cy={node.y}
                        r="32.5"
                        fill={isHighlighted ? 'rgba(255, 87, 34, 0.2)' : 'transparent'}
                        stroke={isHighlighted ? '#ff5722' : 'transparent'}
                        strokeWidth={isHighlighted ? 3 : 0}
                        style={{
                          transition: 'all 0.3s ease',
                        }}
                      />
                      {/* Jyotirlinga icon image */}
                      <image
                        href="/assets/images/jyotirlinga.png"
                        x={node.x - 25}
                        y={node.y - 25}
                        width="50"
                        height="50"
                        className={`node-region-${region} node ${isHighlighted ? 'node-highlighted' : ''}`}
                        style={{
                          cursor: isHighlighted ? 'pointer' : 'default',
                          transition: 'all 0.3s ease',
                          pointerEvents: isHighlighted ? 'auto' : 'none',
                          filter: isHighlighted ? 'drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))' : 'none'
                        }}
                        onClick={() => isHighlighted && handleDestinationClick(nodeId)}
                      />
                    </g>
                  )}

                  {/* For Airports, use the airport icon image */}
                  {isAirport && (
                    <g>
                      {/* Background circle for highlighting */}
                      <circle
                        cx={node.x}
                        cy={node.y}
                        r="31.25"
                        fill={isHighlighted ? 'rgba(255, 87, 34, 0.2)' : 'transparent'}
                        stroke={isHighlighted ? '#ff5722' : 'transparent'}
                        strokeWidth={isHighlighted ? 3 : 0}
                        style={{
                          transition: 'all 0.3s ease',
                        }}
                      />
                      {/* Airport icon image */}
                      <image
                        href="/assets/images/airport.png"
                        x={node.x - 25}
                        y={node.y - 25}
                        width="50"
                        height="50"
                        className={`node-region-${region} node ${isHighlighted ? 'node-highlighted' : ''}`}
                        style={{
                          cursor: isHighlighted ? 'pointer' : 'default',
                          transition: 'all 0.3s ease',
                          pointerEvents: isHighlighted ? 'auto' : 'none',
                          filter: isHighlighted ? 'drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))' : 'none'
                        }}
                        onClick={() => isHighlighted && handleDestinationClick(nodeId)}
                      />
                    </g>
                  )}

                  {/* Regular circles for normal nodes */}
                  {!isJyotirlinga && !isAirport && (
                    <g
                      transform={`translate(${node.x}, ${node.y})`}
                      className={`node-region-${region} node ${isHighlighted ? 'node-highlighted' : ''}`}
                      style={{
                        cursor: isHighlighted ? 'pointer' : 'default',
                        transition: 'all 0.3s ease',
                        pointerEvents: isHighlighted ? 'auto' : 'none',
                      }}
                      onClick={() => isHighlighted && handleDestinationClick(nodeId)}
                    >
                      {createCustomLocationSVG(nodeId, region, isHighlighted)}
                    </g>
                  )}

                  {/* Show energy cubes if nameMode is enabled and this location has cubes */}
                  {nameMode && locationCubes[nodeId] && !isAirport && !isJyotirlinga && (
                    <g>
                      {/* Convert to array if it's not already */}
                      {(Array.isArray(locationCubes[nodeId]) ? locationCubes[nodeId] : [locationCubes[nodeId]]).map((cube, i) => {
                        const color = energyColors[cube] || '#ccc';
                        return (
                          <rect
                            key={`inner-cube-${nodeId}-${i}`}
                            x={node.x - 6.25}
                            y={node.y - 6.25}
                            width="12.5"
                            height="12.5"
                            rx="2.5"
                            fill={color}
                            stroke="#fff"
                            strokeWidth="1"
                          />
                        );
                      })}
                    </g>
                  )}

                  {/* Enhanced location label with better positioned background */}
                  <g className="node-label-container" transform={`translate(${labelX}, ${labelY})`}>
                    <rect
                      x={-labelWidth/2}
                      y="-12"
                      width={labelWidth}
                      height="24"
                      rx="6"
                      ry="6"
                      className="location-label-background"
                      fill="rgba(255, 255, 255, 0.95)"
                      stroke="#ccc"
                      strokeWidth="1"
                    />
                    <text
                      x="0"
                      y="0"
                      textAnchor="middle"
                      dominantBaseline="middle"
                      fontSize="11"
                      fontWeight="bold"
                      fill="#333"
                      className="enhanced-location-label"
                    >
                      {locationName}
                    </text>
                  </g>

                  <title>{`Node ${nodeId} (${location?.name || 'unknown'})`}</title>
                </g>
              );
            })}

            {/* Render location cubes */}
            {renderLocationCubes()}

            {/* Render OM tokens */}
            {renderLocationOM()}

            {/* Render player pieces */}
            {players.map((player, index) => renderPlayerPiece(player, index))}

            {/* Animation for player movement */}
            {renderAnimatingPlayerPiece()}
          </svg>
        )}

        {/* Full-screen overlay */}
        {isFullScreen && (
          <div className="board-fullscreen" style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: '#f0f8ff',
            zIndex: 1000,
            overflow: 'hidden'
          }}>
            {/* Control bar with close button and game controls */}
            <div style={{
              position: 'absolute',
              top: '15px',
              left: '15px',
              zIndex: 10,
              display: 'flex',
              gap: '8px',
              alignItems: 'center'
            }}>
              {/* Close button positioned at the left */}
              <button
                onClick={() => {
                  // If there's a journey modal open in fullscreen, handle it properly
                  if (journeyModal) {
                    // We're already showing the modal in fullscreen, so just clear it and let it reappear
                    setJourneyModal(null);
                  } else {
                    // Check if there's a pending modal we need to clear to prevent duplicates
                    localStorage.removeItem('pendingJourneyModal');
                  }

                  // Exit browser fullscreen mode
                  if (document.fullscreenElement) {
                    document.exitFullscreen().catch(err => {
                      console.error(`Error exiting fullscreen: ${err.message}`);
                    });
                  }

                  setIsBrowserFullScreen(false);
                  setIsFullScreen(false);
                }}
                style={{
                  background: 'rgba(255, 255, 255, 0.8)',
                  borderRadius: '50%',
                  width: '36px',
                  height: '36px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '18px',
                  marginRight: '6px',
                  color: '#333' // Added color for better X visibility
                }}
              >
                ✕
              </button>

              {/* Current turn display (moved here from right side) */}
              <div style={{
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                padding: '6px 12px',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <div style={{
                  fontWeight: 'bold',
                  fontSize: '0.9rem',
                  color: '#333'
                }}>
                  Current Turn:
                </div>
                <div style={{
                  color: 'var(--primary-color)',
                  fontWeight: 'bold',
                  fontSize: '0.9rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}>
                  {currentPlayer ? currentPlayer.name : 'Loading...'}
                  {currentPlayer && socket && currentPlayer.id === socket.id && (
                    <span style={{
                      fontSize: '0.8rem',
                      color: '#666',
                      fontStyle: 'italic',
                      fontWeight: 'normal'
                    }}>
                      (you)
                    </span>
                  )}
                </div>
              </div>

              {/* Game control buttons */}
              <div style={{
                display: 'flex',
                gap: '8px'
              }}>
                {isCurrentPlayer() && (
                  <button
                    onClick={handleEndTurn}
                    className="accent"
                    style={{
                      padding: '6px 12px',
                      fontSize: '0.85rem',
                      backgroundColor: 'var(--accent-color)',
                      color: 'white',
                      borderRadius: '4px',
                      border: 'none',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                      cursor: 'pointer'
                    }}
                  >
                    End Turn
                  </button>
                )}
                <button
                  onClick={() => socket.emit('saveGame')}
                  className="secondary"
                  style={{
                    padding: '6px 12px',
                    fontSize: '0.85rem',
                    backgroundColor: 'var(--secondary-color)',
                    color: 'white',
                    borderRadius: '4px',
                    border: 'none',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                    cursor: 'pointer'
                  }}
                >
                  Save Game
                </button>
                <button
                  onClick={() => setHideSidebars(!hideSidebars)}
                  className="secondary"
                  style={{
                    padding: '6px 12px',
                    fontSize: '0.85rem',
                    backgroundColor: 'var(--secondary-color)',
                    color: 'white',
                    borderRadius: '4px',
                    border: 'none',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                    cursor: 'pointer'
                  }}
                >
                  {hideSidebars ? "Show" : "Hide"}
                </button>
                <button
                  onClick={handleNameModeToggle}
                  className="secondary"
                  style={{
                    padding: '6px 12px',
                    fontSize: '0.85rem',
                    backgroundColor: 'var(--secondary-color)',
                    color: 'white',
                    borderRadius: '4px',
                    border: 'none',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                    cursor: 'pointer'
                  }}
                >
                  {nameMode ? "Show Numbers" : "Show Cubes"}
                </button>
                <button
                  onClick={() => setShowOmTrack(!showOmTrack)}
                  className="secondary"
                  style={{
                    padding: '6px 12px',
                    fontSize: '0.85rem',
                    backgroundColor: 'var(--secondary-color)',
                    color: 'white',
                    borderRadius: '4px',
                    border: 'none',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                    cursor: 'pointer'
                  }}
                >
                  {showOmTrack ? 'Hide Om Track' : 'Show Om Track'}
                </button>
              </div>
            </div>

            {/* Zoom controls moved to bottom left */}
            <div style={{
              position: 'absolute',
              bottom: '15px',
              left: '280px',
              zIndex: 10,
              background: 'rgba(255, 255, 255, 0.7)',
              borderRadius: '8px',
              padding: '5px',
              display: 'flex',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}>
              <button
                onClick={handleZoomIn}
                className="secondary"
                style={{
                  padding: '5px 10px',
                  margin: '0 2px',
                  background: 'transparent',
                  boxShadow: 'none',
                  fontSize: '18px',
                  fontWeight: 'bold',
                  border: 'none',
                  cursor: 'pointer'
                }}
              >
                +
              </button>
              <button
                onClick={handleZoomOut}
                className="secondary"
                style={{
                  padding: '5px 10px',
                  margin: '0 2px',
                  background: 'transparent',
                  boxShadow: 'none',
                  fontSize: '18px',
                  fontWeight: 'bold',
                  border: 'none',
                  cursor: 'pointer'
                }}
              >
                −
              </button>
              <button
                onClick={() => {
                  setZoom(1);
                  setPan({ x: -250, y: 0 });
                }}
                className="secondary"
                style={{
                  padding: '5px 10px',
                  margin: '0 2px',
                  background: 'transparent',
                  boxShadow: 'none',
                  fontSize: '12px',
                  border: 'none',
                  cursor: 'pointer'
                }}
              >
                Reset
              </button>
            </div>

            {/* Left sidebar - Player info and face-up cards */}
            <div style={{
              position: 'absolute',
              top: '70px',
              left: '15px',
              bottom: '15px',
              width: '250px',
              overflowY: 'auto',
              zIndex: 5,
              padding: '5px',
              display: hideSidebars ? 'none' : 'flex',
              flexDirection: 'column',
              gap: '8px',
              transition: 'all 0.3s ease'
            }}>
              {/* Player Mats */}
              <div style={{ marginBottom: '10px' }}>
                <div style={{
                  fontSize: '0.9rem',
                  fontWeight: 'bold',
                  backgroundColor: 'rgba(245, 245, 245, 0.85)',
                  padding: '6px 8px',
                  borderRadius: '8px',
                  marginBottom: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <span>Energy Cubes: </span>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    {Object.entries(gameState.energyCubePile || {}).map(([type, count]) => (
                      <div key={type} style={{ display: 'flex', alignItems: 'center' }}>
                        <div className={`energy-cube ${type}`} style={{ width: '12px', height: '12px', marginRight: '3px' }} />
                        <span style={{ fontSize: '0.8rem' }}>{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
                {players.map((player, index) => (
                  <CompactPlayerMat
                    key={player.id}
                    player={player}
                    isActive={index === turnIndex}
                    winner={winner}
                    socket={socket}
                    gameState={gameState}
                  />
                ))}
              </div>
            </div>

            {/* Bottom left section - Face-up travel and event cards */}
            <div style={{
              position: 'absolute',
              bottom: '15px',
              left: '15px',
              width: '250px',
              zIndex: 5,
              padding: '5px',
              display: hideSidebars ? 'none' : 'block',
              transition: 'all 0.3s ease'
            }}>
              {/* Face-up Cards */}
              <CompactCards
                faceUpTravel={gameState.faceUpTravel}
                faceUpEvent={gameState.faceUpEvent}
                socket={socket}
                currentGlobalEvent={gameState.currentGlobalEvent}
                energyCubePile={gameState.energyCubePile}
                onCardsPicked={handleCardsPicked}
              />
            </div>

            {/* Right sidebar - Face-up journey cards */}
            <div style={{
              position: 'absolute',
              top: '15px',
              right: '15px',
              bottom: '15px',
              width: '420px',
              overflowY: 'auto',
              zIndex: 10,
              padding: '5px',
              display: hideSidebars ? 'none' : 'flex',
              flexDirection: 'column',
              gap: '8px',
              background: 'rgba(255, 255, 255, 0.85)',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              transition: 'all 0.3s ease'
            }}>
              <div style={{
                fontSize: '1rem',
                fontWeight: 'bold',
                marginBottom: '5px',
                padding: '5px',
                borderBottom: '1px solid #ddd',
                textAlign: 'center'
              }}>
                Journey Cards
              </div>
              <CompactJourneyCards
                faceUpJourneyOuter={gameState.faceUpJourneyOuter}
                faceUpJourneyInner={gameState.faceUpJourneyInner}
                gameState={gameState}
                currentPlayer={currentPlayer}
                socket={socket}
                setJourneyModal={setJourneyModal}
              />
            </div>

            {/* Full viewport content area - Adjust width based on sidebar visibility */}
            <div
              className="board-fullscreen-content"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseLeave}
              style={{
                cursor: isDragging ? 'grabbing' : 'grab',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                marginRight: hideSidebars ? '0' : '420px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                transition: 'all 0.3s ease'
              }}
            >
              <svg
                ref={fullScreenSvgRef}
                viewBox="-100 0 1800 1400" // Shift the viewBox left by 100 to avoid right side nodes being cut off
                className={`board-fullscreen-svg ${hideLabels ? 'hide-labels' : ''}`}
                style={{
                  transform: `scale(${zoom}) translate(${pan.x / zoom}px, ${pan.y / zoom}px)`,
                  transformOrigin: 'center center',
                  width: '100%',
                  height: '100%',
                  margin: '0 auto'
                }}
              >
                {/* Add map background image as first element */}
                <image
                  href="/assets/images/map_take_2.jpg"
                  width="1800"
                  height="1400"
                  x="-100"
                  y="0"
                  preserveAspectRatio="xMidYMid slice"
                />

                {/* Draw edges as curved paths instead of straight lines in fullscreen mode */}
                {edges.map((edge) => {
                  const fromNode = nodes[edge.from];
                  const toNode = nodes[edge.to];
                  if (!fromNode || !toNode) return null;

                  // Calculate midpoint
                  const midX = (fromNode.x + toNode.x) / 2;
                  const midY = (fromNode.y + toNode.y) / 2;

                  // Calculate perpendicular offset for control point
                  // Create a perpendicular vector for the curve
                  const dx = toNode.x - fromNode.x;
                  const dy = toNode.y - fromNode.y;
                  const distance = Math.sqrt(dx * dx + dy * dy);

                  // Scale the curve amount based on distance
                  const curveAmount = Math.min(distance * 0.2, 40);

                  // Create perpendicular vector
                  const perpX = -dy / distance * curveAmount;
                  const perpY = dx / distance * curveAmount;

                  // Control point is midpoint plus perpendicular offset
                  const controlX = midX + perpX;
                  const controlY = midY + perpY;

                  // Create SVG path command for quadratic bezier curve
                  const pathData = `M ${fromNode.x} ${fromNode.y} Q ${controlX} ${controlY} ${toNode.x} ${toNode.y}`;

                  return (
                    <path
                      key={`${edge.from}-${edge.to}`}
                      d={pathData}
                      fill="none"
                      stroke="#ffffff"
                      strokeWidth="4.5"
                      strokeLinecap="round"
                      opacity="0.9"
                    />
                  );
                })}

                {/* Draw nodes */}
                {Object.entries(nodes).map(([nodeId, node]) => {
                  nodeId = parseInt(nodeId, 10);
                  const isHighlighted = shouldHighlightNode(nodeId);
                  const nodeColor = getNodeColor(nodeId);
                  const region = getNodeRegion(nodeId);
                  const location = gameState.locations?.find(l => l.id === nodeId);
                  const locationName = location ? location.name : '';

                  // Determine if it's a special node type
                  const isJyotirlinga = nodeId >= 49 && nodeId <= 60;
                  const isAirport = location?.journeyType === 'Airport' || (nodeId >= 61 && nodeId <= 66);

                  // Use the same advanced label positioning for fullscreen mode
                  let labelOffsetX = 0;
                  let labelOffsetY = 40; // Default distance below node

                  // Calculate optimal label position based on region
                  switch(region) {
                    case 'north':
                      labelOffsetY = -45; // Further above node
                      break;
                    case 'south':
                      labelOffsetY = 45; // Further below node
                      break;
                    case 'west':
                      labelOffsetX = -90; // Further left of node
                      labelOffsetY = 0;
                      break;
                    case 'east':
                      labelOffsetX = 70; // Further right of node
                      labelOffsetY = 0;
                      break;
                    case 'central':
                      // Alternate left/right based on node ID parity to reduce overlap
                      labelOffsetX = nodeId % 2 === 0 ? 70 : -70;
                      labelOffsetY = 0;
                      break;
                    case 'northeast':
                      labelOffsetY = -45; // Further above node
                      break;
                    case 'jyotirlinga':
                      labelOffsetX = nodeId % 2 === 0 ? 60 : -60;
                      labelOffsetY = 0;
                      break;
                    case 'airport':
                      // For Airports, position consistently below
                      labelOffsetY = 60;
                      break;
                  }

                  // Special case for node 11 which needs extra offset
                  if (nodeId === 11) {
                    labelOffsetX = -90;
                  }

                  const labelX = node.x + labelOffsetX;
                  const labelY = node.y + labelOffsetY;

                  // Calculate the width needed for the text based on its length for fullscreen mode
                  const textLength = locationName.length;
                  const labelWidth = Math.max(140, Math.min(textLength * 10, 240)); // Slightly wider for fullscreen

                  return (
                    <g key={`node-${nodeId}`} className="node-group">
                      {/* For Jyotirlingas, use the jyotirlinga icon image */}
                      {isJyotirlinga && (
                        <g>
                          {/* Background circle for highlighting */}
                          <circle
                            cx={node.x}
                            cy={node.y}
                            r="32.5"
                            fill={isHighlighted ? 'rgba(255, 87, 34, 0.2)' : 'transparent'}
                            stroke={isHighlighted ? '#ff5722' : 'transparent'}
                            strokeWidth={isHighlighted ? 3 : 0}
                            style={{
                              transition: 'all 0.3s ease',
                            }}
                          />
                          {/* Jyotirlinga icon image */}
                          <image
                            href="/assets/images/jyotirlinga.png"
                            x={node.x - 25}
                            y={node.y - 25}
                            width="50"
                            height="50"
                            className={`node-region-${region} node ${isHighlighted ? 'node-highlighted' : ''}`}
                            style={{
                              cursor: isHighlighted ? 'pointer' : 'default',
                              transition: 'all 0.3s ease',
                              pointerEvents: isHighlighted ? 'auto' : 'none',
                              filter: isHighlighted ? 'drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))' : 'none'
                            }}
                            onClick={() => isHighlighted && handleDestinationClick(nodeId)}
                          />
                        </g>
                      )}

                      {/* For Airports, use the airport icon image */}
                      {isAirport && (
                        <g>
                          {/* Background circle for highlighting */}
                          <circle
                            cx={node.x}
                            cy={node.y}
                            r="31.25"
                            fill={isHighlighted ? 'rgba(255, 87, 34, 0.2)' : 'transparent'}
                            stroke={isHighlighted ? '#ff5722' : 'transparent'}
                            strokeWidth={isHighlighted ? 3 : 0}
                            style={{
                              transition: 'all 0.3s ease',
                            }}
                          />
                          {/* Airport icon image */}
                          <image
                            href="/assets/images/airport.png"
                            x={node.x - 25}
                            y={node.y - 25}
                            width="50"
                            height="50"
                            className={`node-region-${region} node ${isHighlighted ? 'node-highlighted' : ''}`}
                            style={{
                              cursor: isHighlighted ? 'pointer' : 'default',
                              transition: 'all 0.3s ease',
                              pointerEvents: isHighlighted ? 'auto' : 'none',
                              filter: isHighlighted ? 'drop-shadow(0 0 5px rgba(255, 87, 34, 0.7))' : 'none'
                            }}
                            onClick={() => isHighlighted && handleDestinationClick(nodeId)}
                          />
                        </g>
                      )}

                      {/* Regular circles for normal nodes */}
                      {!isJyotirlinga && !isAirport && (
                        <g
                          transform={`translate(${node.x}, ${node.y})`}
                          className={`node-region-${region} node ${isHighlighted ? 'node-highlighted' : ''}`}
                          style={{
                            cursor: isHighlighted ? 'pointer' : 'default',
                            transition: 'all 0.3s ease',
                            pointerEvents: isHighlighted ? 'auto' : 'none',
                          }}
                          onClick={() => isHighlighted && handleDestinationClick(nodeId)}
                        >
                          {createCustomLocationSVG(nodeId, region, isHighlighted)}
                        </g>
                      )}

                      {/* Show either location number or energy cube based on nameMode */}
                      {/* Show energy cubes if nameMode is enabled and this location has cubes */}
                      {nameMode && locationCubes[nodeId] && !isAirport && !isJyotirlinga && (
                        <g>
                          {/* Convert to array if it's not already */}
                          {(Array.isArray(locationCubes[nodeId]) ? locationCubes[nodeId] : [locationCubes[nodeId]]).map((cube, i) => {
                            const color = energyColors[cube] || '#ccc';
                            return (
                              <rect
                                key={`inner-cube-${nodeId}-${i}`}
                                x={node.x - 7.5}
                                y={node.y - 7.5}
                                width="15"
                                height="15"
                                rx="3"
                                fill={color}
                                stroke="#fff"
                                strokeWidth="1"
                              />
                            );
                          })}
                        </g>
                      )}

                      {/* Enhanced location label */}
                      <g className="node-label-container" transform={`translate(${labelX}, ${labelY})`}>
                        <rect
                          x={-labelWidth/2}
                          y="-14"
                          width={labelWidth}
                          height="28"
                          rx="6"
                          ry="6"
                          className="location-label-background"
                          fill="rgba(255, 255, 255, 0.95)"
                          stroke="#333"
                          strokeWidth="1"
                        />
                        <text
                          x="0"
                          y="0"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          fontSize="13"
                          fontWeight="bold"
                          fill="#333"
                          className="enhanced-location-label"
                        >
                          {locationName}
                        </text>
                      </g>

                      <title>{`Node ${nodeId} (${location?.name || 'unknown'})`}</title>
                    </g>
                  );
                })}

                {/* Render players, location cubes, and OM tokens */}
                {players.map((player, index) => renderPlayerPiece(player, index))}

                {/* Animation for player movement in fullscreen mode */}
                {renderAnimatingPlayerPiece()}

                {renderLocationCubes()}
                {renderLocationOM()}
              </svg>
            </div>
          </div>
        )}
      </div>

      {/* Modals - Placed outside all conditional rendering so they work in both modes */}
      {journeyModal && (
        <JourneyCardModal
          card={journeyModal}
          onClose={() => setJourneyModal(null)}
          onCollect={(card) => {
            // Check requirements and cost
            const costArray = [1, 1, 2, 3];
            const journeyCount = getJourneyCount(currentPlayer, card);
            const requiredOm = journeyCount < costArray.length ? costArray[journeyCount] : Infinity;
            const journeyType = card.reward.outer !== undefined ? 'outer' : 'inner';

            // Close modal
            setJourneyModal(null);

            // Emit collect event
            socket.emit('collectJourney', {
              journeyCardId: card.id,
              journeyType,
              requiredOm
            });
          }}
          isAvailable={isCurrentPlayer}
          currentPlayer={currentPlayer}
          gameState={gameState}
        />
      )}

      {/* Travel Card Selection Modal */}
      {showTravelCardSelection && (
        <TravelCardSelection
          player={currentPlayer}
          onCancel={() => setShowTravelCardSelection(false)}
          title="Select Travel Cards"
          description={
            ((gameState.currentGlobalEvent?.effect === 'himalayan_ne' ||
              gameState.currentGlobalEvent?.effect === 'himalayan_ne_end_turn_reward') &&
             getNodeRegion(currentPlayerPos) === 'northeast')
              ? "Himalayan Northeast event active! Your travel cards will only let you move half as far."
              : "Select travel cards for your move. The total value will determine how far you can travel."
          }
          onSelectCards={handleTravelCardSelection}
          numberOfCardsToSelect={100} /* Allow multiple cards for travel, not just 1 */
        />
      )}

      {/* Energy Cube Selection Modal */}
      {showEnergyCubeSelection && (
        <EnergyCubeSelection
          player={currentPlayer}
          onCancel={() => setShowEnergyCubeSelection(false)}
          onSelectCubes={handleEnergyCubeSelection}
          numberOfCubesToSelect={2}
          title="Heavy Haul Event"
          description="Select 2 energy cubes to discard for using the Truck travel card."
        />
      )}

      {showRegionCubeSelection && (
        <EnergyCubeSelection
          player={currentPlayer}
          onCancel={() => setShowRegionCubeSelection(false)}
          onSelectCubes={handleRegionCubeSelection}
          numberOfCubesToSelect={regionSelectionDetails?.count || 2}
          title={`${regionSelectionDetails?.region} Region Effect`}
          description={`Select ${regionSelectionDetails?.count || 2} energy cube(s) to discard for the ${regionSelectionDetails?.effect} effect.`}
        />
      )}

      {showRegionCardSelection && (
        <TravelCardSelection
          player={currentPlayer}
          onCancel={() => setShowRegionCardSelection(false)}
          onSelectCards={handleRegionCardSelection}
          numberOfCardsToSelect={regionSelectionDetails?.count || 1}
          title={regionSelectionDetails?.effect === 'excess_baggage'
            ? 'Hand Limit Exceeded'
            : `${regionSelectionDetails?.region || 'Global'} Region Effect`}
          description={`Select ${regionSelectionDetails?.count || 1} travel card(s) to discard for the ${regionSelectionDetails?.effect} effect.`}
        />
      )}

      {/* Spirit of Seva modal */}
      {spiritOfSevaData && (
        <SpiritOfSevaModal
          data={spiritOfSevaData}
          onClose={() => setSpiritOfSevaData(null)}
        />
      )}

      {/* Card pick animation */}
      {cardPickAnimation.active && (
        <CardPickAnimation
          cards={cardPickAnimation.cards}
          sourcePosition={cardPickAnimation.sourcePosition}
          targetPosition={cardPickAnimation.targetPosition}
          onAnimationComplete={handleAnimationComplete}
        />
      )}

      {/* Add the horizontal Om Turn Track bar */}
      {showOmTrack && (
        <div 
          className="om-track-bar"
          style={{
            position: 'absolute',
            left: isFullScreen ? '100px' : '50px',
            right: isFullScreen ? '100px' : '50px',
            bottom: isFullScreen ? '80px' : '20px', 
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '12px',
            padding: '10px 15px',
            boxShadow: '0 2px 15px rgba(0, 0, 0, 0.3)',
            border: '2px solid var(--primary-color)',
            zIndex: 100,
            display: 'flex',
            flexDirection: 'column',
            transition: 'all 0.3s ease'
          }}
        >
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginBottom: '8px'
          }}>
            <h3 style={{ margin: 0, fontSize: '16px', color: 'var(--primary-color)' }}>Om Turn Track</h3>
            
            <div style={{display: 'flex', alignItems: 'center', gap: '10px'}}>
              <span style={{ fontSize: '12px', color: '#666' }}>
                Turn Order: Higher Position First → Top of Stack First
              </span>
              <button 
                onClick={() => setShowOmTrack(false)}
                style={{
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: 'none',
                  borderRadius: '50%',
                  backgroundColor: 'var(--secondary-color)',
                  color: 'white',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                ✕
              </button>
            </div>
          </div>
          
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-around', 
            alignItems: 'flex-start',
            overflow: 'auto',
            padding: '5px 10px'
          }}>
            {gameState.omTrack && gameState.omTrack.map((stack, pos) => (
              <div 
                key={pos} 
                style={{ 
                  minWidth: '60px',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  margin: '0 5px',
                  position: 'relative'
                }}
              >
                <div style={{ 
                  fontWeight: 'bold',
                  fontSize: '16px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '50%',
                  backgroundColor: stack.includes(currentPlayer?.id) ? '#e74c3c' : 'var(--primary-color)',
                  color: 'white',
                  marginBottom: '5px',
                  boxShadow: stack.includes(currentPlayer?.id) ? '0 0 8px rgba(231, 76, 60, 0.7)' : 'none',
                  border: stack.includes(currentPlayer?.id) ? '2px solid white' : 'none'
                }}>
                  {pos}
                </div>
                {/* Track positions are connected with arrows */}
                {pos < 7 && (
                  <div style={{
                    position: 'absolute',
                    right: '-15px',
                    top: '15px',
                    fontSize: '18px',
                    color: 'var(--primary-color)'
                  }}>
                    →
                  </div>
                )}
                
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '3px',
                  minHeight: '40px',
                  marginTop: '3px'
                }}>
                  {stack.length > 0 ? stack.map((id, idx) => {
                    const player = players.find(p => p.id === id);
                    const playerColors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12'];
                    const color = playerColors[players.findIndex(p => p.id === id) % playerColors.length];
                    const isCurrentPlayer = id === currentPlayer?.id;
                    
                    // Change the styling of the player indicators to emphasize the stacking order
                    // First player in the array (idx=0) is the TOP of the stack (most recently arrived)
                    const isTopOfStack = idx === 0;
                    const opacity = isTopOfStack ? 1 : 0.85 - (idx * 0.1);
                    const scale = isTopOfStack ? 1.1 : 1 - (idx * 0.05);
                    const zIndex = stack.length - idx; // Higher for top of stack
                    
                    return (
                      <div
                        key={`${id}-${idx}`}
                        style={{
                          padding: '3px 6px',
                          backgroundColor: color,
                          color: '#fff',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          boxShadow: isTopOfStack ? '0 2px 3px rgba(0,0,0,0.3)' : 'none',
                          transform: isCurrentPlayer ? 'scale(1.1)' : scale,
                          border: isCurrentPlayer ? '2px solid white' : isTopOfStack ? '1px solid #333' : 'none',
                          maxWidth: '80px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          position: 'relative',
                          opacity: opacity,
                          zIndex: zIndex,
                          marginBottom: '2px'
                        }}
                      >
                        {isTopOfStack && stack.length > 1 && (
                          <div style={{
                            position: 'absolute',
                            top: '-5px',
                            right: '-5px',
                            width: '12px',
                            height: '12px',
                            backgroundColor: '#fff',
                            color: '#333',
                            fontSize: '9px',
                            borderRadius: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontWeight: 'bold',
                            boxShadow: '0 1px 2px rgba(0,0,0,0.3)'
                          }}>
                            1
                          </div>
                        )}
                        {player?.name || id}
                        {isCurrentPlayer && (<span style={{marginLeft: '3px'}}>⭐</span>)}
                        {isTopOfStack && (<span style={{marginLeft: '3px', fontSize: '9px', opacity: 0.8}}>↑</span>)}
                      </div>
                    );
                  }) : (
                    <div style={{ 
                      color: '#999',
                      fontSize: '12px',
                      fontStyle: 'italic'
                    }}>
                      Empty
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Helper: gets the number of journeys already completed at this location
const getJourneyCount = (player, card) => {
  if (!player) return 0;

  // Check if it's inner or outer journey
  const journeyType = card.reward.inner !== undefined ? 'inner' : 'outer';
  const locationId = card.locationId;

  // Count completed journeys of the same type to the same location
  const completedCards = journeyType === 'inner'
    ? player.journeyInner || []
    : player.journeyOuter || [];

  return completedCards.filter(c => c.locationId === locationId).length;
};

export default InteractiveBoard;
